{"name": "tap2go-monorepo", "version": "0.1.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "clean": "turbo run clean", "type-check": "turbo run type-check", "web:dev": "turbo run dev --filter=web", "web:build": "turbo run build --filter=web", "mobile:dev": "turbo run dev --filter=mobile", "mobile:build": "turbo run build --filter=mobile", "mobile:ios": "turbo run ios --filter=mobile", "mobile:android": "turbo run android --filter=mobile"}, "devDependencies": {"turbo": "^2.0.0", "@turbo/gen": "^2.0.0"}, "packageManager": "pnpm@8.15.6", "engines": {"node": ">=18"}}