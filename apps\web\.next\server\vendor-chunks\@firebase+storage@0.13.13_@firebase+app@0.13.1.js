"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@firebase+storage@0.13.13_@firebase+app@0.13.1";
exports.ids = ["vendor-chunks/@firebase+storage@0.13.13_@firebase+app@0.13.1"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@firebase+storage@0.13.13_@firebase+app@0.13.1/node_modules/@firebase/storage/dist/node-esm/index.node.esm.js":
/*!**********************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@firebase+storage@0.13.13_@firebase+app@0.13.1/node_modules/@firebase/storage/dist/node-esm/index.node.esm.js ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StorageError: () => (/* binding */ StorageError),\n/* harmony export */   StorageErrorCode: () => (/* binding */ StorageErrorCode),\n/* harmony export */   StringFormat: () => (/* binding */ StringFormat),\n/* harmony export */   _FbsBlob: () => (/* binding */ FbsBlob),\n/* harmony export */   _Location: () => (/* binding */ Location),\n/* harmony export */   _TaskEvent: () => (/* binding */ TaskEvent),\n/* harmony export */   _TaskState: () => (/* binding */ TaskState),\n/* harmony export */   _UploadTask: () => (/* binding */ UploadTask),\n/* harmony export */   _dataFromString: () => (/* binding */ dataFromString),\n/* harmony export */   _getChild: () => (/* binding */ _getChild),\n/* harmony export */   _invalidArgument: () => (/* binding */ invalidArgument),\n/* harmony export */   _invalidRootOperation: () => (/* binding */ invalidRootOperation),\n/* harmony export */   connectStorageEmulator: () => (/* binding */ connectStorageEmulator),\n/* harmony export */   deleteObject: () => (/* binding */ deleteObject),\n/* harmony export */   getBlob: () => (/* binding */ getBlob),\n/* harmony export */   getBytes: () => (/* binding */ getBytes),\n/* harmony export */   getDownloadURL: () => (/* binding */ getDownloadURL),\n/* harmony export */   getMetadata: () => (/* binding */ getMetadata),\n/* harmony export */   getStorage: () => (/* binding */ getStorage),\n/* harmony export */   getStream: () => (/* binding */ getStream),\n/* harmony export */   list: () => (/* binding */ list),\n/* harmony export */   listAll: () => (/* binding */ listAll),\n/* harmony export */   ref: () => (/* binding */ ref),\n/* harmony export */   updateMetadata: () => (/* binding */ updateMetadata),\n/* harmony export */   uploadBytes: () => (/* binding */ uploadBytes),\n/* harmony export */   uploadBytesResumable: () => (/* binding */ uploadBytesResumable),\n/* harmony export */   uploadString: () => (/* binding */ uploadString)\n/* harmony export */ });\n/* harmony import */ var _firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @firebase/app */ \"(ssr)/../../node_modules/.pnpm/@firebase+app@0.13.1/node_modules/@firebase/app/dist/esm/index.esm2017.js\");\n/* harmony import */ var _firebase_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @firebase/util */ \"(ssr)/../../node_modules/.pnpm/@firebase+util@1.12.0/node_modules/@firebase/util/dist/node-esm/index.node.esm.js\");\n/* harmony import */ var _firebase_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @firebase/component */ \"(ssr)/../../node_modules/.pnpm/@firebase+component@0.6.17/node_modules/@firebase/component/dist/esm/index.esm2017.js\");\n\n\n\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Constants used in the Firebase Storage library.\n */\n/**\n * Domain name for firebase storage.\n */\nconst DEFAULT_HOST = 'firebasestorage.googleapis.com';\n/**\n * The key in Firebase config json for the storage bucket.\n */\nconst CONFIG_STORAGE_BUCKET_KEY = 'storageBucket';\n/**\n * 2 minutes\n *\n * The timeout for all operations except upload.\n */\nconst DEFAULT_MAX_OPERATION_RETRY_TIME = 2 * 60 * 1000;\n/**\n * 10 minutes\n *\n * The timeout for upload.\n */\nconst DEFAULT_MAX_UPLOAD_RETRY_TIME = 10 * 60 * 1000;\n/**\n * 1 second\n */\nconst DEFAULT_MIN_SLEEP_TIME_MILLIS = 1000;\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * An error returned by the Firebase Storage SDK.\n * @public\n */\nclass StorageError extends _firebase_util__WEBPACK_IMPORTED_MODULE_1__.FirebaseError {\n    /**\n     * @param code - A `StorageErrorCode` string to be prefixed with 'storage/' and\n     *  added to the end of the message.\n     * @param message  - Error message.\n     * @param status_ - Corresponding HTTP Status Code\n     */\n    constructor(code, message, status_ = 0) {\n        super(prependCode(code), `Firebase Storage: ${message} (${prependCode(code)})`);\n        this.status_ = status_;\n        /**\n         * Stores custom error data unique to the `StorageError`.\n         */\n        this.customData = { serverResponse: null };\n        this._baseMessage = this.message;\n        // Without this, `instanceof StorageError`, in tests for example,\n        // returns false.\n        Object.setPrototypeOf(this, StorageError.prototype);\n    }\n    get status() {\n        return this.status_;\n    }\n    set status(status) {\n        this.status_ = status;\n    }\n    /**\n     * Compares a `StorageErrorCode` against this error's code, filtering out the prefix.\n     */\n    _codeEquals(code) {\n        return prependCode(code) === this.code;\n    }\n    /**\n     * Optional response message that was added by the server.\n     */\n    get serverResponse() {\n        return this.customData.serverResponse;\n    }\n    set serverResponse(serverResponse) {\n        this.customData.serverResponse = serverResponse;\n        if (this.customData.serverResponse) {\n            this.message = `${this._baseMessage}\\n${this.customData.serverResponse}`;\n        }\n        else {\n            this.message = this._baseMessage;\n        }\n    }\n}\n/**\n * @public\n * Error codes that can be attached to `StorageError` objects.\n */\nvar StorageErrorCode;\n(function (StorageErrorCode) {\n    // Shared between all platforms\n    StorageErrorCode[\"UNKNOWN\"] = \"unknown\";\n    StorageErrorCode[\"OBJECT_NOT_FOUND\"] = \"object-not-found\";\n    StorageErrorCode[\"BUCKET_NOT_FOUND\"] = \"bucket-not-found\";\n    StorageErrorCode[\"PROJECT_NOT_FOUND\"] = \"project-not-found\";\n    StorageErrorCode[\"QUOTA_EXCEEDED\"] = \"quota-exceeded\";\n    StorageErrorCode[\"UNAUTHENTICATED\"] = \"unauthenticated\";\n    StorageErrorCode[\"UNAUTHORIZED\"] = \"unauthorized\";\n    StorageErrorCode[\"UNAUTHORIZED_APP\"] = \"unauthorized-app\";\n    StorageErrorCode[\"RETRY_LIMIT_EXCEEDED\"] = \"retry-limit-exceeded\";\n    StorageErrorCode[\"INVALID_CHECKSUM\"] = \"invalid-checksum\";\n    StorageErrorCode[\"CANCELED\"] = \"canceled\";\n    // JS specific\n    StorageErrorCode[\"INVALID_EVENT_NAME\"] = \"invalid-event-name\";\n    StorageErrorCode[\"INVALID_URL\"] = \"invalid-url\";\n    StorageErrorCode[\"INVALID_DEFAULT_BUCKET\"] = \"invalid-default-bucket\";\n    StorageErrorCode[\"NO_DEFAULT_BUCKET\"] = \"no-default-bucket\";\n    StorageErrorCode[\"CANNOT_SLICE_BLOB\"] = \"cannot-slice-blob\";\n    StorageErrorCode[\"SERVER_FILE_WRONG_SIZE\"] = \"server-file-wrong-size\";\n    StorageErrorCode[\"NO_DOWNLOAD_URL\"] = \"no-download-url\";\n    StorageErrorCode[\"INVALID_ARGUMENT\"] = \"invalid-argument\";\n    StorageErrorCode[\"INVALID_ARGUMENT_COUNT\"] = \"invalid-argument-count\";\n    StorageErrorCode[\"APP_DELETED\"] = \"app-deleted\";\n    StorageErrorCode[\"INVALID_ROOT_OPERATION\"] = \"invalid-root-operation\";\n    StorageErrorCode[\"INVALID_FORMAT\"] = \"invalid-format\";\n    StorageErrorCode[\"INTERNAL_ERROR\"] = \"internal-error\";\n    StorageErrorCode[\"UNSUPPORTED_ENVIRONMENT\"] = \"unsupported-environment\";\n})(StorageErrorCode || (StorageErrorCode = {}));\nfunction prependCode(code) {\n    return 'storage/' + code;\n}\nfunction unknown() {\n    const message = 'An unknown error occurred, please check the error payload for ' +\n        'server response.';\n    return new StorageError(StorageErrorCode.UNKNOWN, message);\n}\nfunction objectNotFound(path) {\n    return new StorageError(StorageErrorCode.OBJECT_NOT_FOUND, \"Object '\" + path + \"' does not exist.\");\n}\nfunction quotaExceeded(bucket) {\n    return new StorageError(StorageErrorCode.QUOTA_EXCEEDED, \"Quota for bucket '\" +\n        bucket +\n        \"' exceeded, please view quota on \" +\n        'https://firebase.google.com/pricing/.');\n}\nfunction unauthenticated() {\n    const message = 'User is not authenticated, please authenticate using Firebase ' +\n        'Authentication and try again.';\n    return new StorageError(StorageErrorCode.UNAUTHENTICATED, message);\n}\nfunction unauthorizedApp() {\n    return new StorageError(StorageErrorCode.UNAUTHORIZED_APP, 'This app does not have permission to access Firebase Storage on this project.');\n}\nfunction unauthorized(path) {\n    return new StorageError(StorageErrorCode.UNAUTHORIZED, \"User does not have permission to access '\" + path + \"'.\");\n}\nfunction retryLimitExceeded() {\n    return new StorageError(StorageErrorCode.RETRY_LIMIT_EXCEEDED, 'Max retry time for operation exceeded, please try again.');\n}\nfunction canceled() {\n    return new StorageError(StorageErrorCode.CANCELED, 'User canceled the upload/download.');\n}\nfunction invalidUrl(url) {\n    return new StorageError(StorageErrorCode.INVALID_URL, \"Invalid URL '\" + url + \"'.\");\n}\nfunction invalidDefaultBucket(bucket) {\n    return new StorageError(StorageErrorCode.INVALID_DEFAULT_BUCKET, \"Invalid default bucket '\" + bucket + \"'.\");\n}\nfunction noDefaultBucket() {\n    return new StorageError(StorageErrorCode.NO_DEFAULT_BUCKET, 'No default bucket ' +\n        \"found. Did you set the '\" +\n        CONFIG_STORAGE_BUCKET_KEY +\n        \"' property when initializing the app?\");\n}\nfunction cannotSliceBlob() {\n    return new StorageError(StorageErrorCode.CANNOT_SLICE_BLOB, 'Cannot slice blob for upload. Please retry the upload.');\n}\nfunction serverFileWrongSize() {\n    return new StorageError(StorageErrorCode.SERVER_FILE_WRONG_SIZE, 'Server recorded incorrect upload file size, please retry the upload.');\n}\nfunction noDownloadURL() {\n    return new StorageError(StorageErrorCode.NO_DOWNLOAD_URL, 'The given file does not have any download URLs.');\n}\n/**\n * @internal\n */\nfunction invalidArgument(message) {\n    return new StorageError(StorageErrorCode.INVALID_ARGUMENT, message);\n}\nfunction appDeleted() {\n    return new StorageError(StorageErrorCode.APP_DELETED, 'The Firebase app was deleted.');\n}\n/**\n * @param name - The name of the operation that was invalid.\n *\n * @internal\n */\nfunction invalidRootOperation(name) {\n    return new StorageError(StorageErrorCode.INVALID_ROOT_OPERATION, \"The operation '\" +\n        name +\n        \"' cannot be performed on a root reference, create a non-root \" +\n        \"reference using child, such as .child('file.png').\");\n}\n/**\n * @param format - The format that was not valid.\n * @param message - A message describing the format violation.\n */\nfunction invalidFormat(format, message) {\n    return new StorageError(StorageErrorCode.INVALID_FORMAT, \"String does not match format '\" + format + \"': \" + message);\n}\n/**\n * @param message - A message describing the internal error.\n */\nfunction internalError(message) {\n    throw new StorageError(StorageErrorCode.INTERNAL_ERROR, 'Internal error: ' + message);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Firebase Storage location data.\n *\n * @internal\n */\nclass Location {\n    constructor(bucket, path) {\n        this.bucket = bucket;\n        this.path_ = path;\n    }\n    get path() {\n        return this.path_;\n    }\n    get isRoot() {\n        return this.path.length === 0;\n    }\n    fullServerUrl() {\n        const encode = encodeURIComponent;\n        return '/b/' + encode(this.bucket) + '/o/' + encode(this.path);\n    }\n    bucketOnlyServerUrl() {\n        const encode = encodeURIComponent;\n        return '/b/' + encode(this.bucket) + '/o';\n    }\n    static makeFromBucketSpec(bucketString, host) {\n        let bucketLocation;\n        try {\n            bucketLocation = Location.makeFromUrl(bucketString, host);\n        }\n        catch (e) {\n            // Not valid URL, use as-is. This lets you put bare bucket names in\n            // config.\n            return new Location(bucketString, '');\n        }\n        if (bucketLocation.path === '') {\n            return bucketLocation;\n        }\n        else {\n            throw invalidDefaultBucket(bucketString);\n        }\n    }\n    static makeFromUrl(url, host) {\n        let location = null;\n        const bucketDomain = '([A-Za-z0-9.\\\\-_]+)';\n        function gsModify(loc) {\n            if (loc.path.charAt(loc.path.length - 1) === '/') {\n                loc.path_ = loc.path_.slice(0, -1);\n            }\n        }\n        const gsPath = '(/(.*))?$';\n        const gsRegex = new RegExp('^gs://' + bucketDomain + gsPath, 'i');\n        const gsIndices = { bucket: 1, path: 3 };\n        function httpModify(loc) {\n            loc.path_ = decodeURIComponent(loc.path);\n        }\n        const version = 'v[A-Za-z0-9_]+';\n        const firebaseStorageHost = host.replace(/[.]/g, '\\\\.');\n        const firebaseStoragePath = '(/([^?#]*).*)?$';\n        const firebaseStorageRegExp = new RegExp(`^https?://${firebaseStorageHost}/${version}/b/${bucketDomain}/o${firebaseStoragePath}`, 'i');\n        const firebaseStorageIndices = { bucket: 1, path: 3 };\n        const cloudStorageHost = host === DEFAULT_HOST\n            ? '(?:storage.googleapis.com|storage.cloud.google.com)'\n            : host;\n        const cloudStoragePath = '([^?#]*)';\n        const cloudStorageRegExp = new RegExp(`^https?://${cloudStorageHost}/${bucketDomain}/${cloudStoragePath}`, 'i');\n        const cloudStorageIndices = { bucket: 1, path: 2 };\n        const groups = [\n            { regex: gsRegex, indices: gsIndices, postModify: gsModify },\n            {\n                regex: firebaseStorageRegExp,\n                indices: firebaseStorageIndices,\n                postModify: httpModify\n            },\n            {\n                regex: cloudStorageRegExp,\n                indices: cloudStorageIndices,\n                postModify: httpModify\n            }\n        ];\n        for (let i = 0; i < groups.length; i++) {\n            const group = groups[i];\n            const captures = group.regex.exec(url);\n            if (captures) {\n                const bucketValue = captures[group.indices.bucket];\n                let pathValue = captures[group.indices.path];\n                if (!pathValue) {\n                    pathValue = '';\n                }\n                location = new Location(bucketValue, pathValue);\n                group.postModify(location);\n                break;\n            }\n        }\n        if (location == null) {\n            throw invalidUrl(url);\n        }\n        return location;\n    }\n}\n\n/**\n * A request whose promise always fails.\n */\nclass FailRequest {\n    constructor(error) {\n        this.promise_ = Promise.reject(error);\n    }\n    /** @inheritDoc */\n    getPromise() {\n        return this.promise_;\n    }\n    /** @inheritDoc */\n    cancel(_appDelete = false) { }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Accepts a callback for an action to perform (`doRequest`),\n * and then a callback for when the backoff has completed (`backoffCompleteCb`).\n * The callback sent to start requires an argument to call (`onRequestComplete`).\n * When `start` calls `doRequest`, it passes a callback for when the request has\n * completed, `onRequestComplete`. Based on this, the backoff continues, with\n * another call to `doRequest` and the above loop continues until the timeout\n * is hit, or a successful response occurs.\n * @description\n * @param doRequest Callback to perform request\n * @param backoffCompleteCb Callback to call when backoff has been completed\n */\nfunction start(doRequest, \n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nbackoffCompleteCb, timeout) {\n    // TODO(andysoto): make this code cleaner (probably refactor into an actual\n    // type instead of a bunch of functions with state shared in the closure)\n    let waitSeconds = 1;\n    // Would type this as \"number\" but that doesn't work for Node so ¯\\_(ツ)_/¯\n    // TODO: find a way to exclude Node type definition for storage because storage only works in browser\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    let retryTimeoutId = null;\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    let globalTimeoutId = null;\n    let hitTimeout = false;\n    let cancelState = 0;\n    function canceled() {\n        return cancelState === 2;\n    }\n    let triggeredCallback = false;\n    function triggerCallback(...args) {\n        if (!triggeredCallback) {\n            triggeredCallback = true;\n            backoffCompleteCb.apply(null, args);\n        }\n    }\n    function callWithDelay(millis) {\n        retryTimeoutId = setTimeout(() => {\n            retryTimeoutId = null;\n            doRequest(responseHandler, canceled());\n        }, millis);\n    }\n    function clearGlobalTimeout() {\n        if (globalTimeoutId) {\n            clearTimeout(globalTimeoutId);\n        }\n    }\n    function responseHandler(success, ...args) {\n        if (triggeredCallback) {\n            clearGlobalTimeout();\n            return;\n        }\n        if (success) {\n            clearGlobalTimeout();\n            triggerCallback.call(null, success, ...args);\n            return;\n        }\n        const mustStop = canceled() || hitTimeout;\n        if (mustStop) {\n            clearGlobalTimeout();\n            triggerCallback.call(null, success, ...args);\n            return;\n        }\n        if (waitSeconds < 64) {\n            /* TODO(andysoto): don't back off so quickly if we know we're offline. */\n            waitSeconds *= 2;\n        }\n        let waitMillis;\n        if (cancelState === 1) {\n            cancelState = 2;\n            waitMillis = 0;\n        }\n        else {\n            waitMillis = (waitSeconds + Math.random()) * 1000;\n        }\n        callWithDelay(waitMillis);\n    }\n    let stopped = false;\n    function stop(wasTimeout) {\n        if (stopped) {\n            return;\n        }\n        stopped = true;\n        clearGlobalTimeout();\n        if (triggeredCallback) {\n            return;\n        }\n        if (retryTimeoutId !== null) {\n            if (!wasTimeout) {\n                cancelState = 2;\n            }\n            clearTimeout(retryTimeoutId);\n            callWithDelay(0);\n        }\n        else {\n            if (!wasTimeout) {\n                cancelState = 1;\n            }\n        }\n    }\n    callWithDelay(0);\n    globalTimeoutId = setTimeout(() => {\n        hitTimeout = true;\n        stop(true);\n    }, timeout);\n    return stop;\n}\n/**\n * Stops the retry loop from repeating.\n * If the function is currently \"in between\" retries, it is invoked immediately\n * with the second parameter as \"true\". Otherwise, it will be invoked once more\n * after the current invocation finishes iff the current invocation would have\n * triggered another retry.\n */\nfunction stop(id) {\n    id(false);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction isJustDef(p) {\n    return p !== void 0;\n}\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction isFunction(p) {\n    return typeof p === 'function';\n}\nfunction isNonArrayObject(p) {\n    return typeof p === 'object' && !Array.isArray(p);\n}\nfunction isString(p) {\n    return typeof p === 'string' || p instanceof String;\n}\nfunction isNativeBlob(p) {\n    return isNativeBlobDefined() && p instanceof Blob;\n}\nfunction isNativeBlobDefined() {\n    return typeof Blob !== 'undefined';\n}\nfunction validateNumber(argument, minValue, maxValue, value) {\n    if (value < minValue) {\n        throw invalidArgument(`Invalid value for '${argument}'. Expected ${minValue} or greater.`);\n    }\n    if (value > maxValue) {\n        throw invalidArgument(`Invalid value for '${argument}'. Expected ${maxValue} or less.`);\n    }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction makeUrl(urlPart, host, protocol) {\n    let origin = host;\n    if (protocol == null) {\n        origin = `https://${host}`;\n    }\n    return `${protocol}://${origin}/v0${urlPart}`;\n}\nfunction makeQueryString(params) {\n    const encode = encodeURIComponent;\n    let queryPart = '?';\n    for (const key in params) {\n        if (params.hasOwnProperty(key)) {\n            const nextPart = encode(key) + '=' + encode(params[key]);\n            queryPart = queryPart + nextPart + '&';\n        }\n    }\n    // Chop off the extra '&' or '?' on the end\n    queryPart = queryPart.slice(0, -1);\n    return queryPart;\n}\n\n/**\n * Error codes for requests made by the XhrIo wrapper.\n */\nvar ErrorCode;\n(function (ErrorCode) {\n    ErrorCode[ErrorCode[\"NO_ERROR\"] = 0] = \"NO_ERROR\";\n    ErrorCode[ErrorCode[\"NETWORK_ERROR\"] = 1] = \"NETWORK_ERROR\";\n    ErrorCode[ErrorCode[\"ABORT\"] = 2] = \"ABORT\";\n})(ErrorCode || (ErrorCode = {}));\n\n/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Checks the status code to see if the action should be retried.\n *\n * @param status Current HTTP status code returned by server.\n * @param additionalRetryCodes additional retry codes to check against\n */\nfunction isRetryStatusCode(status, additionalRetryCodes) {\n    // The codes for which to retry came from this page:\n    // https://cloud.google.com/storage/docs/exponential-backoff\n    const isFiveHundredCode = status >= 500 && status < 600;\n    const extraRetryCodes = [\n        // Request Timeout: web server didn't receive full request in time.\n        408,\n        // Too Many Requests: you're getting rate-limited, basically.\n        429\n    ];\n    const isExtraRetryCode = extraRetryCodes.indexOf(status) !== -1;\n    const isAdditionalRetryCode = additionalRetryCodes.indexOf(status) !== -1;\n    return isFiveHundredCode || isExtraRetryCode || isAdditionalRetryCode;\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Handles network logic for all Storage Requests, including error reporting and\n * retries with backoff.\n *\n * @param I - the type of the backend's network response.\n * @param - O the output type used by the rest of the SDK. The conversion\n * happens in the specified `callback_`.\n */\nclass NetworkRequest {\n    constructor(url_, method_, headers_, body_, successCodes_, additionalRetryCodes_, callback_, errorCallback_, timeout_, progressCallback_, connectionFactory_, retry = true, isUsingEmulator = false) {\n        this.url_ = url_;\n        this.method_ = method_;\n        this.headers_ = headers_;\n        this.body_ = body_;\n        this.successCodes_ = successCodes_;\n        this.additionalRetryCodes_ = additionalRetryCodes_;\n        this.callback_ = callback_;\n        this.errorCallback_ = errorCallback_;\n        this.timeout_ = timeout_;\n        this.progressCallback_ = progressCallback_;\n        this.connectionFactory_ = connectionFactory_;\n        this.retry = retry;\n        this.isUsingEmulator = isUsingEmulator;\n        this.pendingConnection_ = null;\n        this.backoffId_ = null;\n        this.canceled_ = false;\n        this.appDelete_ = false;\n        this.promise_ = new Promise((resolve, reject) => {\n            this.resolve_ = resolve;\n            this.reject_ = reject;\n            this.start_();\n        });\n    }\n    /**\n     * Actually starts the retry loop.\n     */\n    start_() {\n        const doTheRequest = (backoffCallback, canceled) => {\n            if (canceled) {\n                backoffCallback(false, new RequestEndStatus(false, null, true));\n                return;\n            }\n            const connection = this.connectionFactory_();\n            this.pendingConnection_ = connection;\n            const progressListener = progressEvent => {\n                const loaded = progressEvent.loaded;\n                const total = progressEvent.lengthComputable ? progressEvent.total : -1;\n                if (this.progressCallback_ !== null) {\n                    this.progressCallback_(loaded, total);\n                }\n            };\n            if (this.progressCallback_ !== null) {\n                connection.addUploadProgressListener(progressListener);\n            }\n            // connection.send() never rejects, so we don't need to have a error handler or use catch on the returned promise.\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            connection\n                .send(this.url_, this.method_, this.isUsingEmulator, this.body_, this.headers_)\n                .then(() => {\n                if (this.progressCallback_ !== null) {\n                    connection.removeUploadProgressListener(progressListener);\n                }\n                this.pendingConnection_ = null;\n                const hitServer = connection.getErrorCode() === ErrorCode.NO_ERROR;\n                const status = connection.getStatus();\n                if (!hitServer ||\n                    (isRetryStatusCode(status, this.additionalRetryCodes_) &&\n                        this.retry)) {\n                    const wasCanceled = connection.getErrorCode() === ErrorCode.ABORT;\n                    backoffCallback(false, new RequestEndStatus(false, null, wasCanceled));\n                    return;\n                }\n                const successCode = this.successCodes_.indexOf(status) !== -1;\n                backoffCallback(true, new RequestEndStatus(successCode, connection));\n            });\n        };\n        /**\n         * @param requestWentThrough - True if the request eventually went\n         *     through, false if it hit the retry limit or was canceled.\n         */\n        const backoffDone = (requestWentThrough, status) => {\n            const resolve = this.resolve_;\n            const reject = this.reject_;\n            const connection = status.connection;\n            if (status.wasSuccessCode) {\n                try {\n                    const result = this.callback_(connection, connection.getResponse());\n                    if (isJustDef(result)) {\n                        resolve(result);\n                    }\n                    else {\n                        resolve();\n                    }\n                }\n                catch (e) {\n                    reject(e);\n                }\n            }\n            else {\n                if (connection !== null) {\n                    const err = unknown();\n                    err.serverResponse = connection.getErrorText();\n                    if (this.errorCallback_) {\n                        reject(this.errorCallback_(connection, err));\n                    }\n                    else {\n                        reject(err);\n                    }\n                }\n                else {\n                    if (status.canceled) {\n                        const err = this.appDelete_ ? appDeleted() : canceled();\n                        reject(err);\n                    }\n                    else {\n                        const err = retryLimitExceeded();\n                        reject(err);\n                    }\n                }\n            }\n        };\n        if (this.canceled_) {\n            backoffDone(false, new RequestEndStatus(false, null, true));\n        }\n        else {\n            this.backoffId_ = start(doTheRequest, backoffDone, this.timeout_);\n        }\n    }\n    /** @inheritDoc */\n    getPromise() {\n        return this.promise_;\n    }\n    /** @inheritDoc */\n    cancel(appDelete) {\n        this.canceled_ = true;\n        this.appDelete_ = appDelete || false;\n        if (this.backoffId_ !== null) {\n            stop(this.backoffId_);\n        }\n        if (this.pendingConnection_ !== null) {\n            this.pendingConnection_.abort();\n        }\n    }\n}\n/**\n * A collection of information about the result of a network request.\n * @param opt_canceled - Defaults to false.\n */\nclass RequestEndStatus {\n    constructor(wasSuccessCode, connection, canceled) {\n        this.wasSuccessCode = wasSuccessCode;\n        this.connection = connection;\n        this.canceled = !!canceled;\n    }\n}\nfunction addAuthHeader_(headers, authToken) {\n    if (authToken !== null && authToken.length > 0) {\n        headers['Authorization'] = 'Firebase ' + authToken;\n    }\n}\nfunction addVersionHeader_(headers, firebaseVersion) {\n    headers['X-Firebase-Storage-Version'] =\n        'webjs/' + (firebaseVersion !== null && firebaseVersion !== void 0 ? firebaseVersion : 'AppManager');\n}\nfunction addGmpidHeader_(headers, appId) {\n    if (appId) {\n        headers['X-Firebase-GMPID'] = appId;\n    }\n}\nfunction addAppCheckHeader_(headers, appCheckToken) {\n    if (appCheckToken !== null) {\n        headers['X-Firebase-AppCheck'] = appCheckToken;\n    }\n}\nfunction makeRequest(requestInfo, appId, authToken, appCheckToken, requestFactory, firebaseVersion, retry = true, isUsingEmulator = false) {\n    const queryPart = makeQueryString(requestInfo.urlParams);\n    const url = requestInfo.url + queryPart;\n    const headers = Object.assign({}, requestInfo.headers);\n    addGmpidHeader_(headers, appId);\n    addAuthHeader_(headers, authToken);\n    addVersionHeader_(headers, firebaseVersion);\n    addAppCheckHeader_(headers, appCheckToken);\n    return new NetworkRequest(url, requestInfo.method, headers, requestInfo.body, requestInfo.successCodes, requestInfo.additionalRetryCodes, requestInfo.handler, requestInfo.errorHandler, requestInfo.timeout, requestInfo.progressCallback, requestFactory, retry, isUsingEmulator);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction getBlobBuilder() {\n    if (typeof BlobBuilder !== 'undefined') {\n        return BlobBuilder;\n    }\n    else if (typeof WebKitBlobBuilder !== 'undefined') {\n        return WebKitBlobBuilder;\n    }\n    else {\n        return undefined;\n    }\n}\n/**\n * Concatenates one or more values together and converts them to a Blob.\n *\n * @param args The values that will make up the resulting blob.\n * @return The blob.\n */\nfunction getBlob$1(...args) {\n    const BlobBuilder = getBlobBuilder();\n    if (BlobBuilder !== undefined) {\n        const bb = new BlobBuilder();\n        for (let i = 0; i < args.length; i++) {\n            bb.append(args[i]);\n        }\n        return bb.getBlob();\n    }\n    else {\n        if (isNativeBlobDefined()) {\n            return new Blob(args);\n        }\n        else {\n            throw new StorageError(StorageErrorCode.UNSUPPORTED_ENVIRONMENT, \"This browser doesn't seem to support creating Blobs\");\n        }\n    }\n}\n/**\n * Slices the blob. The returned blob contains data from the start byte\n * (inclusive) till the end byte (exclusive). Negative indices cannot be used.\n *\n * @param blob The blob to be sliced.\n * @param start Index of the starting byte.\n * @param end Index of the ending byte.\n * @return The blob slice or null if not supported.\n */\nfunction sliceBlob(blob, start, end) {\n    if (blob.webkitSlice) {\n        return blob.webkitSlice(start, end);\n    }\n    else if (blob.mozSlice) {\n        return blob.mozSlice(start, end);\n    }\n    else if (blob.slice) {\n        return blob.slice(start, end);\n    }\n    return null;\n}\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/** Converts a Base64 encoded string to a binary string. */\nfunction decodeBase64(encoded) {\n    // Node actually doesn't validate base64 strings.\n    // A quick sanity check that is not a fool-proof validation\n    if (/[^-A-Za-z0-9+/=]/.test(encoded)) {\n        throw invalidFormat('base64', 'Invalid character found');\n    }\n    return Buffer.from(encoded, 'base64').toString('binary');\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * An enumeration of the possible string formats for upload.\n * @public\n */\nconst StringFormat = {\n    /**\n     * Indicates the string should be interpreted \"raw\", that is, as normal text.\n     * The string will be interpreted as UTF-16, then uploaded as a UTF-8 byte\n     * sequence.\n     * Example: The string 'Hello! \\\\ud83d\\\\ude0a' becomes the byte sequence\n     * 48 65 6c 6c 6f 21 20 f0 9f 98 8a\n     */\n    RAW: 'raw',\n    /**\n     * Indicates the string should be interpreted as base64-encoded data.\n     * Padding characters (trailing '='s) are optional.\n     * Example: The string 'rWmO++E6t7/rlw==' becomes the byte sequence\n     * ad 69 8e fb e1 3a b7 bf eb 97\n     */\n    BASE64: 'base64',\n    /**\n     * Indicates the string should be interpreted as base64url-encoded data.\n     * Padding characters (trailing '='s) are optional.\n     * Example: The string 'rWmO--E6t7_rlw==' becomes the byte sequence\n     * ad 69 8e fb e1 3a b7 bf eb 97\n     */\n    BASE64URL: 'base64url',\n    /**\n     * Indicates the string is a data URL, such as one obtained from\n     * canvas.toDataURL().\n     * Example: the string 'data:application/octet-stream;base64,aaaa'\n     * becomes the byte sequence\n     * 69 a6 9a\n     * (the content-type \"application/octet-stream\" is also applied, but can\n     * be overridden in the metadata object).\n     */\n    DATA_URL: 'data_url'\n};\nclass StringData {\n    constructor(data, contentType) {\n        this.data = data;\n        this.contentType = contentType || null;\n    }\n}\n/**\n * @internal\n */\nfunction dataFromString(format, stringData) {\n    switch (format) {\n        case StringFormat.RAW:\n            return new StringData(utf8Bytes_(stringData));\n        case StringFormat.BASE64:\n        case StringFormat.BASE64URL:\n            return new StringData(base64Bytes_(format, stringData));\n        case StringFormat.DATA_URL:\n            return new StringData(dataURLBytes_(stringData), dataURLContentType_(stringData));\n        // do nothing\n    }\n    // assert(false);\n    throw unknown();\n}\nfunction utf8Bytes_(value) {\n    const b = [];\n    for (let i = 0; i < value.length; i++) {\n        let c = value.charCodeAt(i);\n        if (c <= 127) {\n            b.push(c);\n        }\n        else {\n            if (c <= 2047) {\n                b.push(192 | (c >> 6), 128 | (c & 63));\n            }\n            else {\n                if ((c & 64512) === 55296) {\n                    // The start of a surrogate pair.\n                    const valid = i < value.length - 1 && (value.charCodeAt(i + 1) & 64512) === 56320;\n                    if (!valid) {\n                        // The second surrogate wasn't there.\n                        b.push(239, 191, 189);\n                    }\n                    else {\n                        const hi = c;\n                        const lo = value.charCodeAt(++i);\n                        c = 65536 | ((hi & 1023) << 10) | (lo & 1023);\n                        b.push(240 | (c >> 18), 128 | ((c >> 12) & 63), 128 | ((c >> 6) & 63), 128 | (c & 63));\n                    }\n                }\n                else {\n                    if ((c & 64512) === 56320) {\n                        // Invalid low surrogate.\n                        b.push(239, 191, 189);\n                    }\n                    else {\n                        b.push(224 | (c >> 12), 128 | ((c >> 6) & 63), 128 | (c & 63));\n                    }\n                }\n            }\n        }\n    }\n    return new Uint8Array(b);\n}\nfunction percentEncodedBytes_(value) {\n    let decoded;\n    try {\n        decoded = decodeURIComponent(value);\n    }\n    catch (e) {\n        throw invalidFormat(StringFormat.DATA_URL, 'Malformed data URL.');\n    }\n    return utf8Bytes_(decoded);\n}\nfunction base64Bytes_(format, value) {\n    switch (format) {\n        case StringFormat.BASE64: {\n            const hasMinus = value.indexOf('-') !== -1;\n            const hasUnder = value.indexOf('_') !== -1;\n            if (hasMinus || hasUnder) {\n                const invalidChar = hasMinus ? '-' : '_';\n                throw invalidFormat(format, \"Invalid character '\" +\n                    invalidChar +\n                    \"' found: is it base64url encoded?\");\n            }\n            break;\n        }\n        case StringFormat.BASE64URL: {\n            const hasPlus = value.indexOf('+') !== -1;\n            const hasSlash = value.indexOf('/') !== -1;\n            if (hasPlus || hasSlash) {\n                const invalidChar = hasPlus ? '+' : '/';\n                throw invalidFormat(format, \"Invalid character '\" + invalidChar + \"' found: is it base64 encoded?\");\n            }\n            value = value.replace(/-/g, '+').replace(/_/g, '/');\n            break;\n        }\n        // do nothing\n    }\n    let bytes;\n    try {\n        bytes = decodeBase64(value);\n    }\n    catch (e) {\n        if (e.message.includes('polyfill')) {\n            throw e;\n        }\n        throw invalidFormat(format, 'Invalid character found');\n    }\n    const array = new Uint8Array(bytes.length);\n    for (let i = 0; i < bytes.length; i++) {\n        array[i] = bytes.charCodeAt(i);\n    }\n    return array;\n}\nclass DataURLParts {\n    constructor(dataURL) {\n        this.base64 = false;\n        this.contentType = null;\n        const matches = dataURL.match(/^data:([^,]+)?,/);\n        if (matches === null) {\n            throw invalidFormat(StringFormat.DATA_URL, \"Must be formatted 'data:[<mediatype>][;base64],<data>\");\n        }\n        const middle = matches[1] || null;\n        if (middle != null) {\n            this.base64 = endsWith(middle, ';base64');\n            this.contentType = this.base64\n                ? middle.substring(0, middle.length - ';base64'.length)\n                : middle;\n        }\n        this.rest = dataURL.substring(dataURL.indexOf(',') + 1);\n    }\n}\nfunction dataURLBytes_(dataUrl) {\n    const parts = new DataURLParts(dataUrl);\n    if (parts.base64) {\n        return base64Bytes_(StringFormat.BASE64, parts.rest);\n    }\n    else {\n        return percentEncodedBytes_(parts.rest);\n    }\n}\nfunction dataURLContentType_(dataUrl) {\n    const parts = new DataURLParts(dataUrl);\n    return parts.contentType;\n}\nfunction endsWith(s, end) {\n    const longEnough = s.length >= end.length;\n    if (!longEnough) {\n        return false;\n    }\n    return s.substring(s.length - end.length) === end;\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @param opt_elideCopy - If true, doesn't copy mutable input data\n *     (e.g. Uint8Arrays). Pass true only if you know the objects will not be\n *     modified after this blob's construction.\n *\n * @internal\n */\nclass FbsBlob {\n    constructor(data, elideCopy) {\n        let size = 0;\n        let blobType = '';\n        if (isNativeBlob(data)) {\n            this.data_ = data;\n            size = data.size;\n            blobType = data.type;\n        }\n        else if (data instanceof ArrayBuffer) {\n            if (elideCopy) {\n                this.data_ = new Uint8Array(data);\n            }\n            else {\n                this.data_ = new Uint8Array(data.byteLength);\n                this.data_.set(new Uint8Array(data));\n            }\n            size = this.data_.length;\n        }\n        else if (data instanceof Uint8Array) {\n            if (elideCopy) {\n                this.data_ = data;\n            }\n            else {\n                this.data_ = new Uint8Array(data.length);\n                this.data_.set(data);\n            }\n            size = data.length;\n        }\n        this.size_ = size;\n        this.type_ = blobType;\n    }\n    size() {\n        return this.size_;\n    }\n    type() {\n        return this.type_;\n    }\n    slice(startByte, endByte) {\n        if (isNativeBlob(this.data_)) {\n            const realBlob = this.data_;\n            const sliced = sliceBlob(realBlob, startByte, endByte);\n            if (sliced === null) {\n                return null;\n            }\n            return new FbsBlob(sliced);\n        }\n        else {\n            const slice = new Uint8Array(this.data_.buffer, startByte, endByte - startByte);\n            return new FbsBlob(slice, true);\n        }\n    }\n    static getBlob(...args) {\n        if (isNativeBlobDefined()) {\n            const blobby = args.map((val) => {\n                if (val instanceof FbsBlob) {\n                    return val.data_;\n                }\n                else {\n                    return val;\n                }\n            });\n            return new FbsBlob(getBlob$1.apply(null, blobby));\n        }\n        else {\n            const uint8Arrays = args.map((val) => {\n                if (isString(val)) {\n                    return dataFromString(StringFormat.RAW, val).data;\n                }\n                else {\n                    // Blobs don't exist, so this has to be a Uint8Array.\n                    return val.data_;\n                }\n            });\n            let finalLength = 0;\n            uint8Arrays.forEach((array) => {\n                finalLength += array.byteLength;\n            });\n            const merged = new Uint8Array(finalLength);\n            let index = 0;\n            uint8Arrays.forEach((array) => {\n                for (let i = 0; i < array.length; i++) {\n                    merged[index++] = array[i];\n                }\n            });\n            return new FbsBlob(merged, true);\n        }\n    }\n    uploadData() {\n        return this.data_;\n    }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Returns the Object resulting from parsing the given JSON, or null if the\n * given string does not represent a JSON object.\n */\nfunction jsonObjectOrNull(s) {\n    let obj;\n    try {\n        obj = JSON.parse(s);\n    }\n    catch (e) {\n        return null;\n    }\n    if (isNonArrayObject(obj)) {\n        return obj;\n    }\n    else {\n        return null;\n    }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Contains helper methods for manipulating paths.\n */\n/**\n * @return Null if the path is already at the root.\n */\nfunction parent(path) {\n    if (path.length === 0) {\n        return null;\n    }\n    const index = path.lastIndexOf('/');\n    if (index === -1) {\n        return '';\n    }\n    const newPath = path.slice(0, index);\n    return newPath;\n}\nfunction child(path, childPath) {\n    const canonicalChildPath = childPath\n        .split('/')\n        .filter(component => component.length > 0)\n        .join('/');\n    if (path.length === 0) {\n        return canonicalChildPath;\n    }\n    else {\n        return path + '/' + canonicalChildPath;\n    }\n}\n/**\n * Returns the last component of a path.\n * '/foo/bar' -> 'bar'\n * '/foo/bar/baz/' -> 'baz/'\n * '/a' -> 'a'\n */\nfunction lastComponent(path) {\n    const index = path.lastIndexOf('/', path.length - 2);\n    if (index === -1) {\n        return path;\n    }\n    else {\n        return path.slice(index + 1);\n    }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction noXform_(metadata, value) {\n    return value;\n}\nclass Mapping {\n    constructor(server, local, writable, xform) {\n        this.server = server;\n        this.local = local || server;\n        this.writable = !!writable;\n        this.xform = xform || noXform_;\n    }\n}\nlet mappings_ = null;\nfunction xformPath(fullPath) {\n    if (!isString(fullPath) || fullPath.length < 2) {\n        return fullPath;\n    }\n    else {\n        return lastComponent(fullPath);\n    }\n}\nfunction getMappings() {\n    if (mappings_) {\n        return mappings_;\n    }\n    const mappings = [];\n    mappings.push(new Mapping('bucket'));\n    mappings.push(new Mapping('generation'));\n    mappings.push(new Mapping('metageneration'));\n    mappings.push(new Mapping('name', 'fullPath', true));\n    function mappingsXformPath(_metadata, fullPath) {\n        return xformPath(fullPath);\n    }\n    const nameMapping = new Mapping('name');\n    nameMapping.xform = mappingsXformPath;\n    mappings.push(nameMapping);\n    /**\n     * Coerces the second param to a number, if it is defined.\n     */\n    function xformSize(_metadata, size) {\n        if (size !== undefined) {\n            return Number(size);\n        }\n        else {\n            return size;\n        }\n    }\n    const sizeMapping = new Mapping('size');\n    sizeMapping.xform = xformSize;\n    mappings.push(sizeMapping);\n    mappings.push(new Mapping('timeCreated'));\n    mappings.push(new Mapping('updated'));\n    mappings.push(new Mapping('md5Hash', null, true));\n    mappings.push(new Mapping('cacheControl', null, true));\n    mappings.push(new Mapping('contentDisposition', null, true));\n    mappings.push(new Mapping('contentEncoding', null, true));\n    mappings.push(new Mapping('contentLanguage', null, true));\n    mappings.push(new Mapping('contentType', null, true));\n    mappings.push(new Mapping('metadata', 'customMetadata', true));\n    mappings_ = mappings;\n    return mappings_;\n}\nfunction addRef(metadata, service) {\n    function generateRef() {\n        const bucket = metadata['bucket'];\n        const path = metadata['fullPath'];\n        const loc = new Location(bucket, path);\n        return service._makeStorageReference(loc);\n    }\n    Object.defineProperty(metadata, 'ref', { get: generateRef });\n}\nfunction fromResource(service, resource, mappings) {\n    const metadata = {};\n    metadata['type'] = 'file';\n    const len = mappings.length;\n    for (let i = 0; i < len; i++) {\n        const mapping = mappings[i];\n        metadata[mapping.local] = mapping.xform(metadata, resource[mapping.server]);\n    }\n    addRef(metadata, service);\n    return metadata;\n}\nfunction fromResourceString(service, resourceString, mappings) {\n    const obj = jsonObjectOrNull(resourceString);\n    if (obj === null) {\n        return null;\n    }\n    const resource = obj;\n    return fromResource(service, resource, mappings);\n}\nfunction downloadUrlFromResourceString(metadata, resourceString, host, protocol) {\n    const obj = jsonObjectOrNull(resourceString);\n    if (obj === null) {\n        return null;\n    }\n    if (!isString(obj['downloadTokens'])) {\n        // This can happen if objects are uploaded through GCS and retrieved\n        // through list, so we don't want to throw an Error.\n        return null;\n    }\n    const tokens = obj['downloadTokens'];\n    if (tokens.length === 0) {\n        return null;\n    }\n    const encode = encodeURIComponent;\n    const tokensList = tokens.split(',');\n    const urls = tokensList.map((token) => {\n        const bucket = metadata['bucket'];\n        const path = metadata['fullPath'];\n        const urlPart = '/b/' + encode(bucket) + '/o/' + encode(path);\n        const base = makeUrl(urlPart, host, protocol);\n        const queryString = makeQueryString({\n            alt: 'media',\n            token\n        });\n        return base + queryString;\n    });\n    return urls[0];\n}\nfunction toResourceString(metadata, mappings) {\n    const resource = {};\n    const len = mappings.length;\n    for (let i = 0; i < len; i++) {\n        const mapping = mappings[i];\n        if (mapping.writable) {\n            resource[mapping.server] = metadata[mapping.local];\n        }\n    }\n    return JSON.stringify(resource);\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst PREFIXES_KEY = 'prefixes';\nconst ITEMS_KEY = 'items';\nfunction fromBackendResponse(service, bucket, resource) {\n    const listResult = {\n        prefixes: [],\n        items: [],\n        nextPageToken: resource['nextPageToken']\n    };\n    if (resource[PREFIXES_KEY]) {\n        for (const path of resource[PREFIXES_KEY]) {\n            const pathWithoutTrailingSlash = path.replace(/\\/$/, '');\n            const reference = service._makeStorageReference(new Location(bucket, pathWithoutTrailingSlash));\n            listResult.prefixes.push(reference);\n        }\n    }\n    if (resource[ITEMS_KEY]) {\n        for (const item of resource[ITEMS_KEY]) {\n            const reference = service._makeStorageReference(new Location(bucket, item['name']));\n            listResult.items.push(reference);\n        }\n    }\n    return listResult;\n}\nfunction fromResponseString(service, bucket, resourceString) {\n    const obj = jsonObjectOrNull(resourceString);\n    if (obj === null) {\n        return null;\n    }\n    const resource = obj;\n    return fromBackendResponse(service, bucket, resource);\n}\n\n/**\n * Contains a fully specified request.\n *\n * @param I - the type of the backend's network response.\n * @param O - the output response type used by the rest of the SDK.\n */\nclass RequestInfo {\n    constructor(url, method, \n    /**\n     * Returns the value with which to resolve the request's promise. Only called\n     * if the request is successful. Throw from this function to reject the\n     * returned Request's promise with the thrown error.\n     * Note: The XhrIo passed to this function may be reused after this callback\n     * returns. Do not keep a reference to it in any way.\n     */\n    handler, timeout) {\n        this.url = url;\n        this.method = method;\n        this.handler = handler;\n        this.timeout = timeout;\n        this.urlParams = {};\n        this.headers = {};\n        this.body = null;\n        this.errorHandler = null;\n        /**\n         * Called with the current number of bytes uploaded and total size (-1 if not\n         * computable) of the request body (i.e. used to report upload progress).\n         */\n        this.progressCallback = null;\n        this.successCodes = [200];\n        this.additionalRetryCodes = [];\n    }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Throws the UNKNOWN StorageError if cndn is false.\n */\nfunction handlerCheck(cndn) {\n    if (!cndn) {\n        throw unknown();\n    }\n}\nfunction metadataHandler(service, mappings) {\n    function handler(xhr, text) {\n        const metadata = fromResourceString(service, text, mappings);\n        handlerCheck(metadata !== null);\n        return metadata;\n    }\n    return handler;\n}\nfunction listHandler(service, bucket) {\n    function handler(xhr, text) {\n        const listResult = fromResponseString(service, bucket, text);\n        handlerCheck(listResult !== null);\n        return listResult;\n    }\n    return handler;\n}\nfunction downloadUrlHandler(service, mappings) {\n    function handler(xhr, text) {\n        const metadata = fromResourceString(service, text, mappings);\n        handlerCheck(metadata !== null);\n        return downloadUrlFromResourceString(metadata, text, service.host, service._protocol);\n    }\n    return handler;\n}\nfunction sharedErrorHandler(location) {\n    function errorHandler(xhr, err) {\n        let newErr;\n        if (xhr.getStatus() === 401) {\n            if (\n            // This exact message string is the only consistent part of the\n            // server's error response that identifies it as an App Check error.\n            xhr.getErrorText().includes('Firebase App Check token is invalid')) {\n                newErr = unauthorizedApp();\n            }\n            else {\n                newErr = unauthenticated();\n            }\n        }\n        else {\n            if (xhr.getStatus() === 402) {\n                newErr = quotaExceeded(location.bucket);\n            }\n            else {\n                if (xhr.getStatus() === 403) {\n                    newErr = unauthorized(location.path);\n                }\n                else {\n                    newErr = err;\n                }\n            }\n        }\n        newErr.status = xhr.getStatus();\n        newErr.serverResponse = err.serverResponse;\n        return newErr;\n    }\n    return errorHandler;\n}\nfunction objectErrorHandler(location) {\n    const shared = sharedErrorHandler(location);\n    function errorHandler(xhr, err) {\n        let newErr = shared(xhr, err);\n        if (xhr.getStatus() === 404) {\n            newErr = objectNotFound(location.path);\n        }\n        newErr.serverResponse = err.serverResponse;\n        return newErr;\n    }\n    return errorHandler;\n}\nfunction getMetadata$2(service, location, mappings) {\n    const urlPart = location.fullServerUrl();\n    const url = makeUrl(urlPart, service.host, service._protocol);\n    const method = 'GET';\n    const timeout = service.maxOperationRetryTime;\n    const requestInfo = new RequestInfo(url, method, metadataHandler(service, mappings), timeout);\n    requestInfo.errorHandler = objectErrorHandler(location);\n    return requestInfo;\n}\nfunction list$2(service, location, delimiter, pageToken, maxResults) {\n    const urlParams = {};\n    if (location.isRoot) {\n        urlParams['prefix'] = '';\n    }\n    else {\n        urlParams['prefix'] = location.path + '/';\n    }\n    if (delimiter && delimiter.length > 0) {\n        urlParams['delimiter'] = delimiter;\n    }\n    if (pageToken) {\n        urlParams['pageToken'] = pageToken;\n    }\n    if (maxResults) {\n        urlParams['maxResults'] = maxResults;\n    }\n    const urlPart = location.bucketOnlyServerUrl();\n    const url = makeUrl(urlPart, service.host, service._protocol);\n    const method = 'GET';\n    const timeout = service.maxOperationRetryTime;\n    const requestInfo = new RequestInfo(url, method, listHandler(service, location.bucket), timeout);\n    requestInfo.urlParams = urlParams;\n    requestInfo.errorHandler = sharedErrorHandler(location);\n    return requestInfo;\n}\nfunction getBytes$1(service, location, maxDownloadSizeBytes) {\n    const urlPart = location.fullServerUrl();\n    const url = makeUrl(urlPart, service.host, service._protocol) + '?alt=media';\n    const method = 'GET';\n    const timeout = service.maxOperationRetryTime;\n    const requestInfo = new RequestInfo(url, method, (_, data) => data, timeout);\n    requestInfo.errorHandler = objectErrorHandler(location);\n    if (maxDownloadSizeBytes !== undefined) {\n        requestInfo.headers['Range'] = `bytes=0-${maxDownloadSizeBytes}`;\n        requestInfo.successCodes = [200 /* OK */, 206 /* Partial Content */];\n    }\n    return requestInfo;\n}\nfunction getDownloadUrl(service, location, mappings) {\n    const urlPart = location.fullServerUrl();\n    const url = makeUrl(urlPart, service.host, service._protocol);\n    const method = 'GET';\n    const timeout = service.maxOperationRetryTime;\n    const requestInfo = new RequestInfo(url, method, downloadUrlHandler(service, mappings), timeout);\n    requestInfo.errorHandler = objectErrorHandler(location);\n    return requestInfo;\n}\nfunction updateMetadata$2(service, location, metadata, mappings) {\n    const urlPart = location.fullServerUrl();\n    const url = makeUrl(urlPart, service.host, service._protocol);\n    const method = 'PATCH';\n    const body = toResourceString(metadata, mappings);\n    const headers = { 'Content-Type': 'application/json; charset=utf-8' };\n    const timeout = service.maxOperationRetryTime;\n    const requestInfo = new RequestInfo(url, method, metadataHandler(service, mappings), timeout);\n    requestInfo.headers = headers;\n    requestInfo.body = body;\n    requestInfo.errorHandler = objectErrorHandler(location);\n    return requestInfo;\n}\nfunction deleteObject$2(service, location) {\n    const urlPart = location.fullServerUrl();\n    const url = makeUrl(urlPart, service.host, service._protocol);\n    const method = 'DELETE';\n    const timeout = service.maxOperationRetryTime;\n    function handler(_xhr, _text) { }\n    const requestInfo = new RequestInfo(url, method, handler, timeout);\n    requestInfo.successCodes = [200, 204];\n    requestInfo.errorHandler = objectErrorHandler(location);\n    return requestInfo;\n}\nfunction determineContentType_(metadata, blob) {\n    return ((metadata && metadata['contentType']) ||\n        (blob && blob.type()) ||\n        'application/octet-stream');\n}\nfunction metadataForUpload_(location, blob, metadata) {\n    const metadataClone = Object.assign({}, metadata);\n    metadataClone['fullPath'] = location.path;\n    metadataClone['size'] = blob.size();\n    if (!metadataClone['contentType']) {\n        metadataClone['contentType'] = determineContentType_(null, blob);\n    }\n    return metadataClone;\n}\n/**\n * Prepare RequestInfo for uploads as Content-Type: multipart.\n */\nfunction multipartUpload(service, location, mappings, blob, metadata) {\n    const urlPart = location.bucketOnlyServerUrl();\n    const headers = {\n        'X-Goog-Upload-Protocol': 'multipart'\n    };\n    function genBoundary() {\n        let str = '';\n        for (let i = 0; i < 2; i++) {\n            str = str + Math.random().toString().slice(2);\n        }\n        return str;\n    }\n    const boundary = genBoundary();\n    headers['Content-Type'] = 'multipart/related; boundary=' + boundary;\n    const metadata_ = metadataForUpload_(location, blob, metadata);\n    const metadataString = toResourceString(metadata_, mappings);\n    const preBlobPart = '--' +\n        boundary +\n        '\\r\\n' +\n        'Content-Type: application/json; charset=utf-8\\r\\n\\r\\n' +\n        metadataString +\n        '\\r\\n--' +\n        boundary +\n        '\\r\\n' +\n        'Content-Type: ' +\n        metadata_['contentType'] +\n        '\\r\\n\\r\\n';\n    const postBlobPart = '\\r\\n--' + boundary + '--';\n    const body = FbsBlob.getBlob(preBlobPart, blob, postBlobPart);\n    if (body === null) {\n        throw cannotSliceBlob();\n    }\n    const urlParams = { name: metadata_['fullPath'] };\n    const url = makeUrl(urlPart, service.host, service._protocol);\n    const method = 'POST';\n    const timeout = service.maxUploadRetryTime;\n    const requestInfo = new RequestInfo(url, method, metadataHandler(service, mappings), timeout);\n    requestInfo.urlParams = urlParams;\n    requestInfo.headers = headers;\n    requestInfo.body = body.uploadData();\n    requestInfo.errorHandler = sharedErrorHandler(location);\n    return requestInfo;\n}\n/**\n * @param current The number of bytes that have been uploaded so far.\n * @param total The total number of bytes in the upload.\n * @param opt_finalized True if the server has finished the upload.\n * @param opt_metadata The upload metadata, should\n *     only be passed if opt_finalized is true.\n */\nclass ResumableUploadStatus {\n    constructor(current, total, finalized, metadata) {\n        this.current = current;\n        this.total = total;\n        this.finalized = !!finalized;\n        this.metadata = metadata || null;\n    }\n}\nfunction checkResumeHeader_(xhr, allowed) {\n    let status = null;\n    try {\n        status = xhr.getResponseHeader('X-Goog-Upload-Status');\n    }\n    catch (e) {\n        handlerCheck(false);\n    }\n    const allowedStatus = allowed || ['active'];\n    handlerCheck(!!status && allowedStatus.indexOf(status) !== -1);\n    return status;\n}\nfunction createResumableUpload(service, location, mappings, blob, metadata) {\n    const urlPart = location.bucketOnlyServerUrl();\n    const metadataForUpload = metadataForUpload_(location, blob, metadata);\n    const urlParams = { name: metadataForUpload['fullPath'] };\n    const url = makeUrl(urlPart, service.host, service._protocol);\n    const method = 'POST';\n    const headers = {\n        'X-Goog-Upload-Protocol': 'resumable',\n        'X-Goog-Upload-Command': 'start',\n        'X-Goog-Upload-Header-Content-Length': `${blob.size()}`,\n        'X-Goog-Upload-Header-Content-Type': metadataForUpload['contentType'],\n        'Content-Type': 'application/json; charset=utf-8'\n    };\n    const body = toResourceString(metadataForUpload, mappings);\n    const timeout = service.maxUploadRetryTime;\n    function handler(xhr) {\n        checkResumeHeader_(xhr);\n        let url;\n        try {\n            url = xhr.getResponseHeader('X-Goog-Upload-URL');\n        }\n        catch (e) {\n            handlerCheck(false);\n        }\n        handlerCheck(isString(url));\n        return url;\n    }\n    const requestInfo = new RequestInfo(url, method, handler, timeout);\n    requestInfo.urlParams = urlParams;\n    requestInfo.headers = headers;\n    requestInfo.body = body;\n    requestInfo.errorHandler = sharedErrorHandler(location);\n    return requestInfo;\n}\n/**\n * @param url From a call to fbs.requests.createResumableUpload.\n */\nfunction getResumableUploadStatus(service, location, url, blob) {\n    const headers = { 'X-Goog-Upload-Command': 'query' };\n    function handler(xhr) {\n        const status = checkResumeHeader_(xhr, ['active', 'final']);\n        let sizeString = null;\n        try {\n            sizeString = xhr.getResponseHeader('X-Goog-Upload-Size-Received');\n        }\n        catch (e) {\n            handlerCheck(false);\n        }\n        if (!sizeString) {\n            // null or empty string\n            handlerCheck(false);\n        }\n        const size = Number(sizeString);\n        handlerCheck(!isNaN(size));\n        return new ResumableUploadStatus(size, blob.size(), status === 'final');\n    }\n    const method = 'POST';\n    const timeout = service.maxUploadRetryTime;\n    const requestInfo = new RequestInfo(url, method, handler, timeout);\n    requestInfo.headers = headers;\n    requestInfo.errorHandler = sharedErrorHandler(location);\n    return requestInfo;\n}\n/**\n * Any uploads via the resumable upload API must transfer a number of bytes\n * that is a multiple of this number.\n */\nconst RESUMABLE_UPLOAD_CHUNK_SIZE = 256 * 1024;\n/**\n * @param url From a call to fbs.requests.createResumableUpload.\n * @param chunkSize Number of bytes to upload.\n * @param status The previous status.\n *     If not passed or null, we start from the beginning.\n * @throws fbs.Error If the upload is already complete, the passed in status\n *     has a final size inconsistent with the blob, or the blob cannot be sliced\n *     for upload.\n */\nfunction continueResumableUpload(location, service, url, blob, chunkSize, mappings, status, progressCallback) {\n    // TODO(andysoto): standardize on internal asserts\n    // assert(!(opt_status && opt_status.finalized));\n    const status_ = new ResumableUploadStatus(0, 0);\n    if (status) {\n        status_.current = status.current;\n        status_.total = status.total;\n    }\n    else {\n        status_.current = 0;\n        status_.total = blob.size();\n    }\n    if (blob.size() !== status_.total) {\n        throw serverFileWrongSize();\n    }\n    const bytesLeft = status_.total - status_.current;\n    let bytesToUpload = bytesLeft;\n    if (chunkSize > 0) {\n        bytesToUpload = Math.min(bytesToUpload, chunkSize);\n    }\n    const startByte = status_.current;\n    const endByte = startByte + bytesToUpload;\n    let uploadCommand = '';\n    if (bytesToUpload === 0) {\n        uploadCommand = 'finalize';\n    }\n    else if (bytesLeft === bytesToUpload) {\n        uploadCommand = 'upload, finalize';\n    }\n    else {\n        uploadCommand = 'upload';\n    }\n    const headers = {\n        'X-Goog-Upload-Command': uploadCommand,\n        'X-Goog-Upload-Offset': `${status_.current}`\n    };\n    const body = blob.slice(startByte, endByte);\n    if (body === null) {\n        throw cannotSliceBlob();\n    }\n    function handler(xhr, text) {\n        // TODO(andysoto): Verify the MD5 of each uploaded range:\n        // the 'x-range-md5' header comes back with status code 308 responses.\n        // We'll only be able to bail out though, because you can't re-upload a\n        // range that you previously uploaded.\n        const uploadStatus = checkResumeHeader_(xhr, ['active', 'final']);\n        const newCurrent = status_.current + bytesToUpload;\n        const size = blob.size();\n        let metadata;\n        if (uploadStatus === 'final') {\n            metadata = metadataHandler(service, mappings)(xhr, text);\n        }\n        else {\n            metadata = null;\n        }\n        return new ResumableUploadStatus(newCurrent, size, uploadStatus === 'final', metadata);\n    }\n    const method = 'POST';\n    const timeout = service.maxUploadRetryTime;\n    const requestInfo = new RequestInfo(url, method, handler, timeout);\n    requestInfo.headers = headers;\n    requestInfo.body = body.uploadData();\n    requestInfo.progressCallback = progressCallback || null;\n    requestInfo.errorHandler = sharedErrorHandler(location);\n    return requestInfo;\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * An event that is triggered on a task.\n * @internal\n */\nconst TaskEvent = {\n    /**\n     * For this event,\n     * <ul>\n     *   <li>The `next` function is triggered on progress updates and when the\n     *       task is paused/resumed with an `UploadTaskSnapshot` as the first\n     *       argument.</li>\n     *   <li>The `error` function is triggered if the upload is canceled or fails\n     *       for another reason.</li>\n     *   <li>The `complete` function is triggered if the upload completes\n     *       successfully.</li>\n     * </ul>\n     */\n    STATE_CHANGED: 'state_changed'\n};\n// type keys = keyof TaskState\n/**\n * Represents the current state of a running upload.\n * @internal\n */\nconst TaskState = {\n    /** The task is currently transferring data. */\n    RUNNING: 'running',\n    /** The task was paused by the user. */\n    PAUSED: 'paused',\n    /** The task completed successfully. */\n    SUCCESS: 'success',\n    /** The task was canceled. */\n    CANCELED: 'canceled',\n    /** The task failed with an error. */\n    ERROR: 'error'\n};\nfunction taskStateFromInternalTaskState(state) {\n    switch (state) {\n        case \"running\" /* InternalTaskState.RUNNING */:\n        case \"pausing\" /* InternalTaskState.PAUSING */:\n        case \"canceling\" /* InternalTaskState.CANCELING */:\n            return TaskState.RUNNING;\n        case \"paused\" /* InternalTaskState.PAUSED */:\n            return TaskState.PAUSED;\n        case \"success\" /* InternalTaskState.SUCCESS */:\n            return TaskState.SUCCESS;\n        case \"canceled\" /* InternalTaskState.CANCELED */:\n            return TaskState.CANCELED;\n        case \"error\" /* InternalTaskState.ERROR */:\n            return TaskState.ERROR;\n        default:\n            // TODO(andysoto): assert(false);\n            return TaskState.ERROR;\n    }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nclass Observer {\n    constructor(nextOrObserver, error, complete) {\n        const asFunctions = isFunction(nextOrObserver) || error != null || complete != null;\n        if (asFunctions) {\n            this.next = nextOrObserver;\n            this.error = error !== null && error !== void 0 ? error : undefined;\n            this.complete = complete !== null && complete !== void 0 ? complete : undefined;\n        }\n        else {\n            const observer = nextOrObserver;\n            this.next = observer.next;\n            this.error = observer.error;\n            this.complete = observer.complete;\n        }\n    }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Returns a function that invokes f with its arguments asynchronously as a\n * microtask, i.e. as soon as possible after the current script returns back\n * into browser code.\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction async(f) {\n    return (...argsToForward) => {\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\n        Promise.resolve().then(() => f(...argsToForward));\n    };\n}\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/** An override for the text-based Connection. Used in tests. */\nlet textFactoryOverride = null;\n/**\n * Network layer that works in Node.\n *\n * This network implementation should not be used in browsers as it does not\n * support progress updates.\n */\nclass FetchConnection {\n    constructor() {\n        this.errorText_ = '';\n        this.sent_ = false;\n        this.errorCode_ = ErrorCode.NO_ERROR;\n    }\n    async send(url, method, isUsingEmulator, body, headers) {\n        if (this.sent_) {\n            throw internalError('cannot .send() more than once');\n        }\n        this.sent_ = true;\n        try {\n            const response = await newFetch(url, method, isUsingEmulator, headers, body);\n            this.headers_ = response.headers;\n            this.statusCode_ = response.status;\n            this.errorCode_ = ErrorCode.NO_ERROR;\n            this.body_ = await response.arrayBuffer();\n        }\n        catch (e) {\n            this.errorText_ = e === null || e === void 0 ? void 0 : e.message;\n            // emulate XHR which sets status to 0 when encountering a network error\n            this.statusCode_ = 0;\n            this.errorCode_ = ErrorCode.NETWORK_ERROR;\n        }\n    }\n    getErrorCode() {\n        if (this.errorCode_ === undefined) {\n            throw internalError('cannot .getErrorCode() before receiving response');\n        }\n        return this.errorCode_;\n    }\n    getStatus() {\n        if (this.statusCode_ === undefined) {\n            throw internalError('cannot .getStatus() before receiving response');\n        }\n        return this.statusCode_;\n    }\n    getErrorText() {\n        return this.errorText_;\n    }\n    abort() {\n        // Not supported\n    }\n    getResponseHeader(header) {\n        if (!this.headers_) {\n            throw internalError('cannot .getResponseHeader() before receiving response');\n        }\n        return this.headers_.get(header);\n    }\n    addUploadProgressListener(listener) {\n        // Not supported\n    }\n    removeUploadProgressListener(listener) {\n        // Not supported\n    }\n}\nclass FetchTextConnection extends FetchConnection {\n    getResponse() {\n        if (!this.body_) {\n            throw internalError('cannot .getResponse() before receiving response');\n        }\n        return Buffer.from(this.body_).toString('utf-8');\n    }\n}\nfunction newTextConnection() {\n    return textFactoryOverride\n        ? textFactoryOverride()\n        : new FetchTextConnection();\n}\nclass FetchBytesConnection extends FetchConnection {\n    getResponse() {\n        if (!this.body_) {\n            throw internalError('cannot .getResponse() before sending');\n        }\n        return this.body_;\n    }\n}\nfunction newBytesConnection() {\n    return new FetchBytesConnection();\n}\nclass FetchStreamConnection extends FetchConnection {\n    constructor() {\n        super(...arguments);\n        this.stream_ = null;\n    }\n    async send(url, method, isUsingEmulator, body, headers) {\n        if (this.sent_) {\n            throw internalError('cannot .send() more than once');\n        }\n        this.sent_ = true;\n        try {\n            const response = await newFetch(url, method, isUsingEmulator, headers, body);\n            this.headers_ = response.headers;\n            this.statusCode_ = response.status;\n            this.errorCode_ = ErrorCode.NO_ERROR;\n            this.stream_ = response.body;\n        }\n        catch (e) {\n            this.errorText_ = e === null || e === void 0 ? void 0 : e.message;\n            // emulate XHR which sets status to 0 when encountering a network error\n            this.statusCode_ = 0;\n            this.errorCode_ = ErrorCode.NETWORK_ERROR;\n        }\n    }\n    getResponse() {\n        if (!this.stream_) {\n            throw internalError('cannot .getResponse() before sending');\n        }\n        return this.stream_;\n    }\n}\nfunction newFetch(url, method, isUsingEmulator, headers, body) {\n    const fetchArgs = {\n        method,\n        headers: headers || {},\n        body: body\n    };\n    if ((0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.isCloudWorkstation)(url) && isUsingEmulator) {\n        fetchArgs.credentials = 'include';\n    }\n    return fetch(url, fetchArgs);\n}\nfunction newStreamConnection() {\n    return new FetchStreamConnection();\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Represents a blob being uploaded. Can be used to pause/resume/cancel the\n * upload and manage callbacks for various events.\n * @internal\n */\nclass UploadTask {\n    isExponentialBackoffExpired() {\n        return this.sleepTime > this.maxSleepTime;\n    }\n    /**\n     * @param ref - The firebaseStorage.Reference object this task came\n     *     from, untyped to avoid cyclic dependencies.\n     * @param blob - The blob to upload.\n     */\n    constructor(ref, blob, metadata = null) {\n        /**\n         * Number of bytes transferred so far.\n         */\n        this._transferred = 0;\n        this._needToFetchStatus = false;\n        this._needToFetchMetadata = false;\n        this._observers = [];\n        this._error = undefined;\n        this._uploadUrl = undefined;\n        this._request = undefined;\n        this._chunkMultiplier = 1;\n        this._resolve = undefined;\n        this._reject = undefined;\n        this._ref = ref;\n        this._blob = blob;\n        this._metadata = metadata;\n        this._mappings = getMappings();\n        this._resumable = this._shouldDoResumable(this._blob);\n        this._state = \"running\" /* InternalTaskState.RUNNING */;\n        this._errorHandler = error => {\n            this._request = undefined;\n            this._chunkMultiplier = 1;\n            if (error._codeEquals(StorageErrorCode.CANCELED)) {\n                this._needToFetchStatus = true;\n                this.completeTransitions_();\n            }\n            else {\n                const backoffExpired = this.isExponentialBackoffExpired();\n                if (isRetryStatusCode(error.status, [])) {\n                    if (backoffExpired) {\n                        error = retryLimitExceeded();\n                    }\n                    else {\n                        this.sleepTime = Math.max(this.sleepTime * 2, DEFAULT_MIN_SLEEP_TIME_MILLIS);\n                        this._needToFetchStatus = true;\n                        this.completeTransitions_();\n                        return;\n                    }\n                }\n                this._error = error;\n                this._transition(\"error\" /* InternalTaskState.ERROR */);\n            }\n        };\n        this._metadataErrorHandler = error => {\n            this._request = undefined;\n            if (error._codeEquals(StorageErrorCode.CANCELED)) {\n                this.completeTransitions_();\n            }\n            else {\n                this._error = error;\n                this._transition(\"error\" /* InternalTaskState.ERROR */);\n            }\n        };\n        this.sleepTime = 0;\n        this.maxSleepTime = this._ref.storage.maxUploadRetryTime;\n        this._promise = new Promise((resolve, reject) => {\n            this._resolve = resolve;\n            this._reject = reject;\n            this._start();\n        });\n        // Prevent uncaught rejections on the internal promise from bubbling out\n        // to the top level with a dummy handler.\n        this._promise.then(null, () => { });\n    }\n    _makeProgressCallback() {\n        const sizeBefore = this._transferred;\n        return loaded => this._updateProgress(sizeBefore + loaded);\n    }\n    _shouldDoResumable(blob) {\n        return blob.size() > 256 * 1024;\n    }\n    _start() {\n        if (this._state !== \"running\" /* InternalTaskState.RUNNING */) {\n            // This can happen if someone pauses us in a resume callback, for example.\n            return;\n        }\n        if (this._request !== undefined) {\n            return;\n        }\n        if (this._resumable) {\n            if (this._uploadUrl === undefined) {\n                this._createResumable();\n            }\n            else {\n                if (this._needToFetchStatus) {\n                    this._fetchStatus();\n                }\n                else {\n                    if (this._needToFetchMetadata) {\n                        // Happens if we miss the metadata on upload completion.\n                        this._fetchMetadata();\n                    }\n                    else {\n                        this.pendingTimeout = setTimeout(() => {\n                            this.pendingTimeout = undefined;\n                            this._continueUpload();\n                        }, this.sleepTime);\n                    }\n                }\n            }\n        }\n        else {\n            this._oneShotUpload();\n        }\n    }\n    _resolveToken(callback) {\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\n        Promise.all([\n            this._ref.storage._getAuthToken(),\n            this._ref.storage._getAppCheckToken()\n        ]).then(([authToken, appCheckToken]) => {\n            switch (this._state) {\n                case \"running\" /* InternalTaskState.RUNNING */:\n                    callback(authToken, appCheckToken);\n                    break;\n                case \"canceling\" /* InternalTaskState.CANCELING */:\n                    this._transition(\"canceled\" /* InternalTaskState.CANCELED */);\n                    break;\n                case \"pausing\" /* InternalTaskState.PAUSING */:\n                    this._transition(\"paused\" /* InternalTaskState.PAUSED */);\n                    break;\n            }\n        });\n    }\n    // TODO(andysoto): assert false\n    _createResumable() {\n        this._resolveToken((authToken, appCheckToken) => {\n            const requestInfo = createResumableUpload(this._ref.storage, this._ref._location, this._mappings, this._blob, this._metadata);\n            const createRequest = this._ref.storage._makeRequest(requestInfo, newTextConnection, authToken, appCheckToken);\n            this._request = createRequest;\n            createRequest.getPromise().then((url) => {\n                this._request = undefined;\n                this._uploadUrl = url;\n                this._needToFetchStatus = false;\n                this.completeTransitions_();\n            }, this._errorHandler);\n        });\n    }\n    _fetchStatus() {\n        // TODO(andysoto): assert(this.uploadUrl_ !== null);\n        const url = this._uploadUrl;\n        this._resolveToken((authToken, appCheckToken) => {\n            const requestInfo = getResumableUploadStatus(this._ref.storage, this._ref._location, url, this._blob);\n            const statusRequest = this._ref.storage._makeRequest(requestInfo, newTextConnection, authToken, appCheckToken);\n            this._request = statusRequest;\n            statusRequest.getPromise().then(status => {\n                status = status;\n                this._request = undefined;\n                this._updateProgress(status.current);\n                this._needToFetchStatus = false;\n                if (status.finalized) {\n                    this._needToFetchMetadata = true;\n                }\n                this.completeTransitions_();\n            }, this._errorHandler);\n        });\n    }\n    _continueUpload() {\n        const chunkSize = RESUMABLE_UPLOAD_CHUNK_SIZE * this._chunkMultiplier;\n        const status = new ResumableUploadStatus(this._transferred, this._blob.size());\n        // TODO(andysoto): assert(this.uploadUrl_ !== null);\n        const url = this._uploadUrl;\n        this._resolveToken((authToken, appCheckToken) => {\n            let requestInfo;\n            try {\n                requestInfo = continueResumableUpload(this._ref._location, this._ref.storage, url, this._blob, chunkSize, this._mappings, status, this._makeProgressCallback());\n            }\n            catch (e) {\n                this._error = e;\n                this._transition(\"error\" /* InternalTaskState.ERROR */);\n                return;\n            }\n            const uploadRequest = this._ref.storage._makeRequest(requestInfo, newTextConnection, authToken, appCheckToken, \n            /*retry=*/ false // Upload requests should not be retried as each retry should be preceded by another query request. Which is handled in this file.\n            );\n            this._request = uploadRequest;\n            uploadRequest.getPromise().then((newStatus) => {\n                this._increaseMultiplier();\n                this._request = undefined;\n                this._updateProgress(newStatus.current);\n                if (newStatus.finalized) {\n                    this._metadata = newStatus.metadata;\n                    this._transition(\"success\" /* InternalTaskState.SUCCESS */);\n                }\n                else {\n                    this.completeTransitions_();\n                }\n            }, this._errorHandler);\n        });\n    }\n    _increaseMultiplier() {\n        const currentSize = RESUMABLE_UPLOAD_CHUNK_SIZE * this._chunkMultiplier;\n        // Max chunk size is 32M.\n        if (currentSize * 2 < 32 * 1024 * 1024) {\n            this._chunkMultiplier *= 2;\n        }\n    }\n    _fetchMetadata() {\n        this._resolveToken((authToken, appCheckToken) => {\n            const requestInfo = getMetadata$2(this._ref.storage, this._ref._location, this._mappings);\n            const metadataRequest = this._ref.storage._makeRequest(requestInfo, newTextConnection, authToken, appCheckToken);\n            this._request = metadataRequest;\n            metadataRequest.getPromise().then(metadata => {\n                this._request = undefined;\n                this._metadata = metadata;\n                this._transition(\"success\" /* InternalTaskState.SUCCESS */);\n            }, this._metadataErrorHandler);\n        });\n    }\n    _oneShotUpload() {\n        this._resolveToken((authToken, appCheckToken) => {\n            const requestInfo = multipartUpload(this._ref.storage, this._ref._location, this._mappings, this._blob, this._metadata);\n            const multipartRequest = this._ref.storage._makeRequest(requestInfo, newTextConnection, authToken, appCheckToken);\n            this._request = multipartRequest;\n            multipartRequest.getPromise().then(metadata => {\n                this._request = undefined;\n                this._metadata = metadata;\n                this._updateProgress(this._blob.size());\n                this._transition(\"success\" /* InternalTaskState.SUCCESS */);\n            }, this._errorHandler);\n        });\n    }\n    _updateProgress(transferred) {\n        const old = this._transferred;\n        this._transferred = transferred;\n        // A progress update can make the \"transferred\" value smaller (e.g. a\n        // partial upload not completed by server, after which the \"transferred\"\n        // value may reset to the value at the beginning of the request).\n        if (this._transferred !== old) {\n            this._notifyObservers();\n        }\n    }\n    _transition(state) {\n        if (this._state === state) {\n            return;\n        }\n        switch (state) {\n            case \"canceling\" /* InternalTaskState.CANCELING */:\n            case \"pausing\" /* InternalTaskState.PAUSING */:\n                // TODO(andysoto):\n                // assert(this.state_ === InternalTaskState.RUNNING ||\n                //        this.state_ === InternalTaskState.PAUSING);\n                this._state = state;\n                if (this._request !== undefined) {\n                    this._request.cancel();\n                }\n                else if (this.pendingTimeout) {\n                    clearTimeout(this.pendingTimeout);\n                    this.pendingTimeout = undefined;\n                    this.completeTransitions_();\n                }\n                break;\n            case \"running\" /* InternalTaskState.RUNNING */:\n                // TODO(andysoto):\n                // assert(this.state_ === InternalTaskState.PAUSED ||\n                //        this.state_ === InternalTaskState.PAUSING);\n                const wasPaused = this._state === \"paused\" /* InternalTaskState.PAUSED */;\n                this._state = state;\n                if (wasPaused) {\n                    this._notifyObservers();\n                    this._start();\n                }\n                break;\n            case \"paused\" /* InternalTaskState.PAUSED */:\n                // TODO(andysoto):\n                // assert(this.state_ === InternalTaskState.PAUSING);\n                this._state = state;\n                this._notifyObservers();\n                break;\n            case \"canceled\" /* InternalTaskState.CANCELED */:\n                // TODO(andysoto):\n                // assert(this.state_ === InternalTaskState.PAUSED ||\n                //        this.state_ === InternalTaskState.CANCELING);\n                this._error = canceled();\n                this._state = state;\n                this._notifyObservers();\n                break;\n            case \"error\" /* InternalTaskState.ERROR */:\n                // TODO(andysoto):\n                // assert(this.state_ === InternalTaskState.RUNNING ||\n                //        this.state_ === InternalTaskState.PAUSING ||\n                //        this.state_ === InternalTaskState.CANCELING);\n                this._state = state;\n                this._notifyObservers();\n                break;\n            case \"success\" /* InternalTaskState.SUCCESS */:\n                // TODO(andysoto):\n                // assert(this.state_ === InternalTaskState.RUNNING ||\n                //        this.state_ === InternalTaskState.PAUSING ||\n                //        this.state_ === InternalTaskState.CANCELING);\n                this._state = state;\n                this._notifyObservers();\n                break;\n        }\n    }\n    completeTransitions_() {\n        switch (this._state) {\n            case \"pausing\" /* InternalTaskState.PAUSING */:\n                this._transition(\"paused\" /* InternalTaskState.PAUSED */);\n                break;\n            case \"canceling\" /* InternalTaskState.CANCELING */:\n                this._transition(\"canceled\" /* InternalTaskState.CANCELED */);\n                break;\n            case \"running\" /* InternalTaskState.RUNNING */:\n                this._start();\n                break;\n        }\n    }\n    /**\n     * A snapshot of the current task state.\n     */\n    get snapshot() {\n        const externalState = taskStateFromInternalTaskState(this._state);\n        return {\n            bytesTransferred: this._transferred,\n            totalBytes: this._blob.size(),\n            state: externalState,\n            metadata: this._metadata,\n            task: this,\n            ref: this._ref\n        };\n    }\n    /**\n     * Adds a callback for an event.\n     * @param type - The type of event to listen for.\n     * @param nextOrObserver -\n     *     The `next` function, which gets called for each item in\n     *     the event stream, or an observer object with some or all of these three\n     *     properties (`next`, `error`, `complete`).\n     * @param error - A function that gets called with a `StorageError`\n     *     if the event stream ends due to an error.\n     * @param completed - A function that gets called if the\n     *     event stream ends normally.\n     * @returns\n     *     If only the event argument is passed, returns a function you can use to\n     *     add callbacks (see the examples above). If more than just the event\n     *     argument is passed, returns a function you can call to unregister the\n     *     callbacks.\n     */\n    on(type, nextOrObserver, error, completed) {\n        // Note: `type` isn't being used. Its type is also incorrect. TaskEvent should not be a string.\n        const observer = new Observer(nextOrObserver || undefined, error || undefined, completed || undefined);\n        this._addObserver(observer);\n        return () => {\n            this._removeObserver(observer);\n        };\n    }\n    /**\n     * This object behaves like a Promise, and resolves with its snapshot data\n     * when the upload completes.\n     * @param onFulfilled - The fulfillment callback. Promise chaining works as normal.\n     * @param onRejected - The rejection callback.\n     */\n    then(onFulfilled, onRejected) {\n        // These casts are needed so that TypeScript can infer the types of the\n        // resulting Promise.\n        return this._promise.then(onFulfilled, onRejected);\n    }\n    /**\n     * Equivalent to calling `then(null, onRejected)`.\n     */\n    catch(onRejected) {\n        return this.then(null, onRejected);\n    }\n    /**\n     * Adds the given observer.\n     */\n    _addObserver(observer) {\n        this._observers.push(observer);\n        this._notifyObserver(observer);\n    }\n    /**\n     * Removes the given observer.\n     */\n    _removeObserver(observer) {\n        const i = this._observers.indexOf(observer);\n        if (i !== -1) {\n            this._observers.splice(i, 1);\n        }\n    }\n    _notifyObservers() {\n        this._finishPromise();\n        const observers = this._observers.slice();\n        observers.forEach(observer => {\n            this._notifyObserver(observer);\n        });\n    }\n    _finishPromise() {\n        if (this._resolve !== undefined) {\n            let triggered = true;\n            switch (taskStateFromInternalTaskState(this._state)) {\n                case TaskState.SUCCESS:\n                    async(this._resolve.bind(null, this.snapshot))();\n                    break;\n                case TaskState.CANCELED:\n                case TaskState.ERROR:\n                    const toCall = this._reject;\n                    async(toCall.bind(null, this._error))();\n                    break;\n                default:\n                    triggered = false;\n                    break;\n            }\n            if (triggered) {\n                this._resolve = undefined;\n                this._reject = undefined;\n            }\n        }\n    }\n    _notifyObserver(observer) {\n        const externalState = taskStateFromInternalTaskState(this._state);\n        switch (externalState) {\n            case TaskState.RUNNING:\n            case TaskState.PAUSED:\n                if (observer.next) {\n                    async(observer.next.bind(observer, this.snapshot))();\n                }\n                break;\n            case TaskState.SUCCESS:\n                if (observer.complete) {\n                    async(observer.complete.bind(observer))();\n                }\n                break;\n            case TaskState.CANCELED:\n            case TaskState.ERROR:\n                if (observer.error) {\n                    async(observer.error.bind(observer, this._error))();\n                }\n                break;\n            default:\n                // TODO(andysoto): assert(false);\n                if (observer.error) {\n                    async(observer.error.bind(observer, this._error))();\n                }\n        }\n    }\n    /**\n     * Resumes a paused task. Has no effect on a currently running or failed task.\n     * @returns True if the operation took effect, false if ignored.\n     */\n    resume() {\n        const valid = this._state === \"paused\" /* InternalTaskState.PAUSED */ ||\n            this._state === \"pausing\" /* InternalTaskState.PAUSING */;\n        if (valid) {\n            this._transition(\"running\" /* InternalTaskState.RUNNING */);\n        }\n        return valid;\n    }\n    /**\n     * Pauses a currently running task. Has no effect on a paused or failed task.\n     * @returns True if the operation took effect, false if ignored.\n     */\n    pause() {\n        const valid = this._state === \"running\" /* InternalTaskState.RUNNING */;\n        if (valid) {\n            this._transition(\"pausing\" /* InternalTaskState.PAUSING */);\n        }\n        return valid;\n    }\n    /**\n     * Cancels a currently running or paused task. Has no effect on a complete or\n     * failed task.\n     * @returns True if the operation took effect, false if ignored.\n     */\n    cancel() {\n        const valid = this._state === \"running\" /* InternalTaskState.RUNNING */ ||\n            this._state === \"pausing\" /* InternalTaskState.PAUSING */;\n        if (valid) {\n            this._transition(\"canceling\" /* InternalTaskState.CANCELING */);\n        }\n        return valid;\n    }\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Provides methods to interact with a bucket in the Firebase Storage service.\n * @internal\n * @param _location - An fbs.location, or the URL at\n *     which to base this object, in one of the following forms:\n *         gs://<bucket>/<object-path>\n *         http[s]://firebasestorage.googleapis.com/\n *                     <api-version>/b/<bucket>/o/<object-path>\n *     Any query or fragment strings will be ignored in the http[s]\n *     format. If no value is passed, the storage object will use a URL based on\n *     the project ID of the base firebase.App instance.\n */\nclass Reference {\n    constructor(_service, location) {\n        this._service = _service;\n        if (location instanceof Location) {\n            this._location = location;\n        }\n        else {\n            this._location = Location.makeFromUrl(location, _service.host);\n        }\n    }\n    /**\n     * Returns the URL for the bucket and path this object references,\n     *     in the form gs://<bucket>/<object-path>\n     * @override\n     */\n    toString() {\n        return 'gs://' + this._location.bucket + '/' + this._location.path;\n    }\n    _newRef(service, location) {\n        return new Reference(service, location);\n    }\n    /**\n     * A reference to the root of this object's bucket.\n     */\n    get root() {\n        const location = new Location(this._location.bucket, '');\n        return this._newRef(this._service, location);\n    }\n    /**\n     * The name of the bucket containing this reference's object.\n     */\n    get bucket() {\n        return this._location.bucket;\n    }\n    /**\n     * The full path of this object.\n     */\n    get fullPath() {\n        return this._location.path;\n    }\n    /**\n     * The short name of this object, which is the last component of the full path.\n     * For example, if fullPath is 'full/path/image.png', name is 'image.png'.\n     */\n    get name() {\n        return lastComponent(this._location.path);\n    }\n    /**\n     * The `StorageService` instance this `StorageReference` is associated with.\n     */\n    get storage() {\n        return this._service;\n    }\n    /**\n     * A `StorageReference` pointing to the parent location of this `StorageReference`, or null if\n     * this reference is the root.\n     */\n    get parent() {\n        const newPath = parent(this._location.path);\n        if (newPath === null) {\n            return null;\n        }\n        const location = new Location(this._location.bucket, newPath);\n        return new Reference(this._service, location);\n    }\n    /**\n     * Utility function to throw an error in methods that do not accept a root reference.\n     */\n    _throwIfRoot(name) {\n        if (this._location.path === '') {\n            throw invalidRootOperation(name);\n        }\n    }\n}\n/**\n * Download the bytes at the object's location.\n * @returns A Promise containing the downloaded bytes.\n */\nfunction getBytesInternal(ref, maxDownloadSizeBytes) {\n    ref._throwIfRoot('getBytes');\n    const requestInfo = getBytes$1(ref.storage, ref._location, maxDownloadSizeBytes);\n    return ref.storage\n        .makeRequestWithTokens(requestInfo, newBytesConnection)\n        .then(bytes => maxDownloadSizeBytes !== undefined\n        ? // GCS may not honor the Range header for small files\n            bytes.slice(0, maxDownloadSizeBytes)\n        : bytes);\n}\n/** Stream the bytes at the object's location. */\nfunction getStreamInternal(ref, maxDownloadSizeBytes) {\n    ref._throwIfRoot('getStream');\n    const requestInfo = getBytes$1(ref.storage, ref._location, maxDownloadSizeBytes);\n    // Transforms the stream so that only `maxDownloadSizeBytes` bytes are piped to the result\n    const newMaxSizeTransform = (n) => {\n        let missingBytes = n;\n        return {\n            transform(chunk, controller) {\n                // GCS may not honor the Range header for small files\n                if (chunk.length < missingBytes) {\n                    controller.enqueue(chunk);\n                    missingBytes -= chunk.length;\n                }\n                else {\n                    controller.enqueue(chunk.slice(0, missingBytes));\n                    controller.terminate();\n                }\n            }\n        };\n    };\n    const result = maxDownloadSizeBytes !== undefined\n        ? new TransformStream(newMaxSizeTransform(maxDownloadSizeBytes))\n        : new TransformStream(); // The default transformer forwards all chunks to its readable side\n    ref.storage\n        .makeRequestWithTokens(requestInfo, newStreamConnection)\n        .then(readableStream => readableStream.pipeThrough(result))\n        .catch(err => result.writable.abort(err));\n    return result.readable;\n}\n/**\n * Uploads data to this object's location.\n * The upload is not resumable.\n *\n * @param ref - StorageReference where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the newly uploaded data.\n * @returns A Promise containing an UploadResult\n */\nfunction uploadBytes$1(ref, data, metadata) {\n    ref._throwIfRoot('uploadBytes');\n    const requestInfo = multipartUpload(ref.storage, ref._location, getMappings(), new FbsBlob(data, true), metadata);\n    return ref.storage\n        .makeRequestWithTokens(requestInfo, newTextConnection)\n        .then(finalMetadata => {\n        return {\n            metadata: finalMetadata,\n            ref\n        };\n    });\n}\n/**\n * Uploads data to this object's location.\n * The upload can be paused and resumed, and exposes progress updates.\n * @public\n * @param ref - StorageReference where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the newly uploaded data.\n * @returns An UploadTask\n */\nfunction uploadBytesResumable$1(ref, data, metadata) {\n    ref._throwIfRoot('uploadBytesResumable');\n    return new UploadTask(ref, new FbsBlob(data), metadata);\n}\n/**\n * Uploads a string to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - StorageReference where string should be uploaded.\n * @param value - The string to upload.\n * @param format - The format of the string to upload.\n * @param metadata - Metadata for the newly uploaded string.\n * @returns A Promise containing an UploadResult\n */\nfunction uploadString$1(ref, value, format = StringFormat.RAW, metadata) {\n    ref._throwIfRoot('uploadString');\n    const data = dataFromString(format, value);\n    const metadataClone = Object.assign({}, metadata);\n    if (metadataClone['contentType'] == null && data.contentType != null) {\n        metadataClone['contentType'] = data.contentType;\n    }\n    return uploadBytes$1(ref, data.data, metadataClone);\n}\n/**\n * List all items (files) and prefixes (folders) under this storage reference.\n *\n * This is a helper method for calling list() repeatedly until there are\n * no more results. The default pagination size is 1000.\n *\n * Note: The results may not be consistent if objects are changed while this\n * operation is running.\n *\n * Warning: listAll may potentially consume too many resources if there are\n * too many results.\n * @public\n * @param ref - StorageReference to get list from.\n *\n * @returns A Promise that resolves with all the items and prefixes under\n *      the current storage reference. `prefixes` contains references to\n *      sub-directories and `items` contains references to objects in this\n *      folder. `nextPageToken` is never returned.\n */\nfunction listAll$1(ref) {\n    const accumulator = {\n        prefixes: [],\n        items: []\n    };\n    return listAllHelper(ref, accumulator).then(() => accumulator);\n}\n/**\n * Separated from listAll because async functions can't use \"arguments\".\n * @param ref\n * @param accumulator\n * @param pageToken\n */\nasync function listAllHelper(ref, accumulator, pageToken) {\n    const opt = {\n        // maxResults is 1000 by default.\n        pageToken\n    };\n    const nextPage = await list$1(ref, opt);\n    accumulator.prefixes.push(...nextPage.prefixes);\n    accumulator.items.push(...nextPage.items);\n    if (nextPage.nextPageToken != null) {\n        await listAllHelper(ref, accumulator, nextPage.nextPageToken);\n    }\n}\n/**\n * List items (files) and prefixes (folders) under this storage reference.\n *\n * List API is only available for Firebase Rules Version 2.\n *\n * GCS is a key-blob store. Firebase Storage imposes the semantic of '/'\n * delimited folder structure.\n * Refer to GCS's List API if you want to learn more.\n *\n * To adhere to Firebase Rules's Semantics, Firebase Storage does not\n * support objects whose paths end with \"/\" or contain two consecutive\n * \"/\"s. Firebase Storage List API will filter these unsupported objects.\n * list() may fail if there are too many unsupported objects in the bucket.\n * @public\n *\n * @param ref - StorageReference to get list from.\n * @param options - See ListOptions for details.\n * @returns A Promise that resolves with the items and prefixes.\n *      `prefixes` contains references to sub-folders and `items`\n *      contains references to objects in this folder. `nextPageToken`\n *      can be used to get the rest of the results.\n */\nfunction list$1(ref, options) {\n    if (options != null) {\n        if (typeof options.maxResults === 'number') {\n            validateNumber('options.maxResults', \n            /* minValue= */ 1, \n            /* maxValue= */ 1000, options.maxResults);\n        }\n    }\n    const op = options || {};\n    const requestInfo = list$2(ref.storage, ref._location, \n    /*delimiter= */ '/', op.pageToken, op.maxResults);\n    return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n/**\n * A `Promise` that resolves with the metadata for this object. If this\n * object doesn't exist or metadata cannot be retrieved, the promise is\n * rejected.\n * @public\n * @param ref - StorageReference to get metadata from.\n */\nfunction getMetadata$1(ref) {\n    ref._throwIfRoot('getMetadata');\n    const requestInfo = getMetadata$2(ref.storage, ref._location, getMappings());\n    return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n/**\n * Updates the metadata for this object.\n * @public\n * @param ref - StorageReference to update metadata for.\n * @param metadata - The new metadata for the object.\n *     Only values that have been explicitly set will be changed. Explicitly\n *     setting a value to null will remove the metadata.\n * @returns A `Promise` that resolves\n *     with the new metadata for this object.\n *     See `firebaseStorage.Reference.prototype.getMetadata`\n */\nfunction updateMetadata$1(ref, metadata) {\n    ref._throwIfRoot('updateMetadata');\n    const requestInfo = updateMetadata$2(ref.storage, ref._location, metadata, getMappings());\n    return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n/**\n * Returns the download URL for the given Reference.\n * @public\n * @returns A `Promise` that resolves with the download\n *     URL for this object.\n */\nfunction getDownloadURL$1(ref) {\n    ref._throwIfRoot('getDownloadURL');\n    const requestInfo = getDownloadUrl(ref.storage, ref._location, getMappings());\n    return ref.storage\n        .makeRequestWithTokens(requestInfo, newTextConnection)\n        .then(url => {\n        if (url === null) {\n            throw noDownloadURL();\n        }\n        return url;\n    });\n}\n/**\n * Deletes the object at this location.\n * @public\n * @param ref - StorageReference for object to delete.\n * @returns A `Promise` that resolves if the deletion succeeds.\n */\nfunction deleteObject$1(ref) {\n    ref._throwIfRoot('deleteObject');\n    const requestInfo = deleteObject$2(ref.storage, ref._location);\n    return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n/**\n * Returns reference for object obtained by appending `childPath` to `ref`.\n *\n * @param ref - StorageReference to get child of.\n * @param childPath - Child path from provided ref.\n * @returns A reference to the object obtained by\n * appending childPath, removing any duplicate, beginning, or trailing\n * slashes.\n *\n */\nfunction _getChild$1(ref, childPath) {\n    const newPath = child(ref._location.path, childPath);\n    const location = new Location(ref._location.bucket, newPath);\n    return new Reference(ref.storage, location);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction isUrl(path) {\n    return /^[A-Za-z]+:\\/\\//.test(path);\n}\n/**\n * Returns a firebaseStorage.Reference for the given url.\n */\nfunction refFromURL(service, url) {\n    return new Reference(service, url);\n}\n/**\n * Returns a firebaseStorage.Reference for the given path in the default\n * bucket.\n */\nfunction refFromPath(ref, path) {\n    if (ref instanceof FirebaseStorageImpl) {\n        const service = ref;\n        if (service._bucket == null) {\n            throw noDefaultBucket();\n        }\n        const reference = new Reference(service, service._bucket);\n        if (path != null) {\n            return refFromPath(reference, path);\n        }\n        else {\n            return reference;\n        }\n    }\n    else {\n        // ref is a Reference\n        if (path !== undefined) {\n            return _getChild$1(ref, path);\n        }\n        else {\n            return ref;\n        }\n    }\n}\nfunction ref$1(serviceOrRef, pathOrUrl) {\n    if (pathOrUrl && isUrl(pathOrUrl)) {\n        if (serviceOrRef instanceof FirebaseStorageImpl) {\n            return refFromURL(serviceOrRef, pathOrUrl);\n        }\n        else {\n            throw invalidArgument('To use ref(service, url), the first argument must be a Storage instance.');\n        }\n    }\n    else {\n        return refFromPath(serviceOrRef, pathOrUrl);\n    }\n}\nfunction extractBucket(host, config) {\n    const bucketString = config === null || config === void 0 ? void 0 : config[CONFIG_STORAGE_BUCKET_KEY];\n    if (bucketString == null) {\n        return null;\n    }\n    return Location.makeFromBucketSpec(bucketString, host);\n}\nfunction connectStorageEmulator$1(storage, host, port, options = {}) {\n    storage.host = `${host}:${port}`;\n    const useSsl = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.isCloudWorkstation)(host);\n    // Workaround to get cookies in Firebase Studio\n    if (useSsl) {\n        void (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.pingServer)(`https://${storage.host}/b`);\n        (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.updateEmulatorBanner)('Storage', true);\n    }\n    storage._isUsingEmulator = true;\n    storage._protocol = useSsl ? 'https' : 'http';\n    const { mockUserToken } = options;\n    if (mockUserToken) {\n        storage._overrideAuthToken =\n            typeof mockUserToken === 'string'\n                ? mockUserToken\n                : (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.createMockUserToken)(mockUserToken, storage.app.options.projectId);\n    }\n}\n/**\n * A service that provides Firebase Storage Reference instances.\n * @param opt_url - gs:// url to a custom Storage Bucket\n *\n * @internal\n */\nclass FirebaseStorageImpl {\n    constructor(\n    /**\n     * FirebaseApp associated with this StorageService instance.\n     */\n    app, _authProvider, \n    /**\n     * @internal\n     */\n    _appCheckProvider, \n    /**\n     * @internal\n     */\n    _url, _firebaseVersion, _isUsingEmulator = false) {\n        this.app = app;\n        this._authProvider = _authProvider;\n        this._appCheckProvider = _appCheckProvider;\n        this._url = _url;\n        this._firebaseVersion = _firebaseVersion;\n        this._isUsingEmulator = _isUsingEmulator;\n        this._bucket = null;\n        /**\n         * This string can be in the formats:\n         * - host\n         * - host:port\n         */\n        this._host = DEFAULT_HOST;\n        this._protocol = 'https';\n        this._appId = null;\n        this._deleted = false;\n        this._maxOperationRetryTime = DEFAULT_MAX_OPERATION_RETRY_TIME;\n        this._maxUploadRetryTime = DEFAULT_MAX_UPLOAD_RETRY_TIME;\n        this._requests = new Set();\n        if (_url != null) {\n            this._bucket = Location.makeFromBucketSpec(_url, this._host);\n        }\n        else {\n            this._bucket = extractBucket(this._host, this.app.options);\n        }\n    }\n    /**\n     * The host string for this service, in the form of `host` or\n     * `host:port`.\n     */\n    get host() {\n        return this._host;\n    }\n    set host(host) {\n        this._host = host;\n        if (this._url != null) {\n            this._bucket = Location.makeFromBucketSpec(this._url, host);\n        }\n        else {\n            this._bucket = extractBucket(host, this.app.options);\n        }\n    }\n    /**\n     * The maximum time to retry uploads in milliseconds.\n     */\n    get maxUploadRetryTime() {\n        return this._maxUploadRetryTime;\n    }\n    set maxUploadRetryTime(time) {\n        validateNumber('time', \n        /* minValue=*/ 0, \n        /* maxValue= */ Number.POSITIVE_INFINITY, time);\n        this._maxUploadRetryTime = time;\n    }\n    /**\n     * The maximum time to retry operations other than uploads or downloads in\n     * milliseconds.\n     */\n    get maxOperationRetryTime() {\n        return this._maxOperationRetryTime;\n    }\n    set maxOperationRetryTime(time) {\n        validateNumber('time', \n        /* minValue=*/ 0, \n        /* maxValue= */ Number.POSITIVE_INFINITY, time);\n        this._maxOperationRetryTime = time;\n    }\n    async _getAuthToken() {\n        if (this._overrideAuthToken) {\n            return this._overrideAuthToken;\n        }\n        const auth = this._authProvider.getImmediate({ optional: true });\n        if (auth) {\n            const tokenData = await auth.getToken();\n            if (tokenData !== null) {\n                return tokenData.accessToken;\n            }\n        }\n        return null;\n    }\n    async _getAppCheckToken() {\n        if ((0,_firebase_app__WEBPACK_IMPORTED_MODULE_0__._isFirebaseServerApp)(this.app) && this.app.settings.appCheckToken) {\n            return this.app.settings.appCheckToken;\n        }\n        const appCheck = this._appCheckProvider.getImmediate({ optional: true });\n        if (appCheck) {\n            const result = await appCheck.getToken();\n            // TODO: What do we want to do if there is an error getting the token?\n            // Context: appCheck.getToken() will never throw even if an error happened. In the error case, a dummy token will be\n            // returned along with an error field describing the error. In general, we shouldn't care about the error condition and just use\n            // the token (actual or dummy) to send requests.\n            return result.token;\n        }\n        return null;\n    }\n    /**\n     * Stop running requests and prevent more from being created.\n     */\n    _delete() {\n        if (!this._deleted) {\n            this._deleted = true;\n            this._requests.forEach(request => request.cancel());\n            this._requests.clear();\n        }\n        return Promise.resolve();\n    }\n    /**\n     * Returns a new firebaseStorage.Reference object referencing this StorageService\n     * at the given Location.\n     */\n    _makeStorageReference(loc) {\n        return new Reference(this, loc);\n    }\n    /**\n     * @param requestInfo - HTTP RequestInfo object\n     * @param authToken - Firebase auth token\n     */\n    _makeRequest(requestInfo, requestFactory, authToken, appCheckToken, retry = true) {\n        if (!this._deleted) {\n            const request = makeRequest(requestInfo, this._appId, authToken, appCheckToken, requestFactory, this._firebaseVersion, retry, this._isUsingEmulator);\n            this._requests.add(request);\n            // Request removes itself from set when complete.\n            request.getPromise().then(() => this._requests.delete(request), () => this._requests.delete(request));\n            return request;\n        }\n        else {\n            return new FailRequest(appDeleted());\n        }\n    }\n    async makeRequestWithTokens(requestInfo, requestFactory) {\n        const [authToken, appCheckToken] = await Promise.all([\n            this._getAuthToken(),\n            this._getAppCheckToken()\n        ]);\n        return this._makeRequest(requestInfo, requestFactory, authToken, appCheckToken).getPromise();\n    }\n}\n\nconst name = \"@firebase/storage\";\nconst version = \"0.13.13\";\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Type constant for Firebase Storage.\n */\nconst STORAGE_TYPE = 'storage';\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Downloads the data at the object's location. Returns an error if the object\n * is not found.\n *\n * To use this functionality, you have to whitelist your app's origin in your\n * Cloud Storage bucket. See also\n * https://cloud.google.com/storage/docs/configuring-cors\n *\n * @public\n * @param ref - StorageReference where data should be downloaded.\n * @param maxDownloadSizeBytes - If set, the maximum allowed size in bytes to\n * retrieve.\n * @returns A Promise containing the object's bytes\n */\nfunction getBytes(ref, maxDownloadSizeBytes) {\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\n    return getBytesInternal(ref, maxDownloadSizeBytes);\n}\n/**\n * Uploads data to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - {@link StorageReference} where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the data to upload.\n * @returns A Promise containing an UploadResult\n */\nfunction uploadBytes(ref, data, metadata) {\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\n    return uploadBytes$1(ref, data, metadata);\n}\n/**\n * Uploads a string to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - {@link StorageReference} where string should be uploaded.\n * @param value - The string to upload.\n * @param format - The format of the string to upload.\n * @param metadata - Metadata for the string to upload.\n * @returns A Promise containing an UploadResult\n */\nfunction uploadString(ref, value, format, metadata) {\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\n    return uploadString$1(ref, value, format, metadata);\n}\n/**\n * Uploads data to this object's location.\n * The upload can be paused and resumed, and exposes progress updates.\n * @public\n * @param ref - {@link StorageReference} where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the data to upload.\n * @returns An UploadTask\n */\nfunction uploadBytesResumable(ref, data, metadata) {\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\n    return uploadBytesResumable$1(ref, data, metadata);\n}\n/**\n * A `Promise` that resolves with the metadata for this object. If this\n * object doesn't exist or metadata cannot be retrieved, the promise is\n * rejected.\n * @public\n * @param ref - {@link StorageReference} to get metadata from.\n */\nfunction getMetadata(ref) {\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\n    return getMetadata$1(ref);\n}\n/**\n * Updates the metadata for this object.\n * @public\n * @param ref - {@link StorageReference} to update metadata for.\n * @param metadata - The new metadata for the object.\n *     Only values that have been explicitly set will be changed. Explicitly\n *     setting a value to null will remove the metadata.\n * @returns A `Promise` that resolves with the new metadata for this object.\n */\nfunction updateMetadata(ref, metadata) {\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\n    return updateMetadata$1(ref, metadata);\n}\n/**\n * List items (files) and prefixes (folders) under this storage reference.\n *\n * List API is only available for Firebase Rules Version 2.\n *\n * GCS is a key-blob store. Firebase Storage imposes the semantic of '/'\n * delimited folder structure.\n * Refer to GCS's List API if you want to learn more.\n *\n * To adhere to Firebase Rules's Semantics, Firebase Storage does not\n * support objects whose paths end with \"/\" or contain two consecutive\n * \"/\"s. Firebase Storage List API will filter these unsupported objects.\n * list() may fail if there are too many unsupported objects in the bucket.\n * @public\n *\n * @param ref - {@link StorageReference} to get list from.\n * @param options - See {@link ListOptions} for details.\n * @returns A `Promise` that resolves with the items and prefixes.\n *      `prefixes` contains references to sub-folders and `items`\n *      contains references to objects in this folder. `nextPageToken`\n *      can be used to get the rest of the results.\n */\nfunction list(ref, options) {\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\n    return list$1(ref, options);\n}\n/**\n * List all items (files) and prefixes (folders) under this storage reference.\n *\n * This is a helper method for calling list() repeatedly until there are\n * no more results. The default pagination size is 1000.\n *\n * Note: The results may not be consistent if objects are changed while this\n * operation is running.\n *\n * Warning: `listAll` may potentially consume too many resources if there are\n * too many results.\n * @public\n * @param ref - {@link StorageReference} to get list from.\n *\n * @returns A `Promise` that resolves with all the items and prefixes under\n *      the current storage reference. `prefixes` contains references to\n *      sub-directories and `items` contains references to objects in this\n *      folder. `nextPageToken` is never returned.\n */\nfunction listAll(ref) {\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\n    return listAll$1(ref);\n}\n/**\n * Returns the download URL for the given {@link StorageReference}.\n * @public\n * @param ref - {@link StorageReference} to get the download URL for.\n * @returns A `Promise` that resolves with the download\n *     URL for this object.\n */\nfunction getDownloadURL(ref) {\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\n    return getDownloadURL$1(ref);\n}\n/**\n * Deletes the object at this location.\n * @public\n * @param ref - {@link StorageReference} for object to delete.\n * @returns A `Promise` that resolves if the deletion succeeds.\n */\nfunction deleteObject(ref) {\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\n    return deleteObject$1(ref);\n}\nfunction ref(serviceOrRef, pathOrUrl) {\n    serviceOrRef = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(serviceOrRef);\n    return ref$1(serviceOrRef, pathOrUrl);\n}\n/**\n * @internal\n */\nfunction _getChild(ref, childPath) {\n    return _getChild$1(ref, childPath);\n}\n/**\n * Gets a {@link FirebaseStorage} instance for the given Firebase app.\n * @public\n * @param app - Firebase app to get {@link FirebaseStorage} instance for.\n * @param bucketUrl - The gs:// url to your Firebase Storage Bucket.\n * If not passed, uses the app's default Storage Bucket.\n * @returns A {@link FirebaseStorage} instance.\n */\nfunction getStorage(app = (0,_firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApp)(), bucketUrl) {\n    app = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(app);\n    const storageProvider = (0,_firebase_app__WEBPACK_IMPORTED_MODULE_0__._getProvider)(app, STORAGE_TYPE);\n    const storageInstance = storageProvider.getImmediate({\n        identifier: bucketUrl\n    });\n    const emulator = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getDefaultEmulatorHostnameAndPort)('storage');\n    if (emulator) {\n        connectStorageEmulator(storageInstance, ...emulator);\n    }\n    return storageInstance;\n}\n/**\n * Modify this {@link FirebaseStorage} instance to communicate with the Cloud Storage emulator.\n *\n * @param storage - The {@link FirebaseStorage} instance\n * @param host - The emulator host (ex: localhost)\n * @param port - The emulator port (ex: 5001)\n * @param options - Emulator options. `options.mockUserToken` is the mock auth\n * token to use for unit testing Security Rules.\n * @public\n */\nfunction connectStorageEmulator(storage, host, port, options = {}) {\n    connectStorageEmulator$1(storage, host, port, options);\n}\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Downloads the data at the object's location. Returns an error if the object\n * is not found.\n *\n * To use this functionality, you have to whitelist your app's origin in your\n * Cloud Storage bucket. See also\n * https://cloud.google.com/storage/docs/configuring-cors\n *\n * This API is not available in Node.\n *\n * @public\n * @param ref - StorageReference where data should be downloaded.\n * @param maxDownloadSizeBytes - If set, the maximum allowed size in bytes to\n * retrieve.\n * @returns A Promise that resolves with a Blob containing the object's bytes\n */\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction getBlob(ref, maxDownloadSizeBytes) {\n    throw new Error('getBlob() is only available in Browser-like environments');\n}\n/**\n * Downloads the data at the object's location. Raises an error event if the\n * object is not found.\n *\n * This API is only available in Node.\n *\n * @public\n * @param ref - StorageReference where data should be downloaded.\n * @param maxDownloadSizeBytes - If set, the maximum allowed size in bytes to\n * retrieve.\n * @returns A stream with the object's data as bytes\n */\nfunction getStream(ref, maxDownloadSizeBytes) {\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\n    return getStreamInternal(ref, maxDownloadSizeBytes);\n}\n\n/**\n * Cloud Storage for Firebase\n *\n * @packageDocumentation\n */\nfunction factory(container, { instanceIdentifier: url }) {\n    const app = container.getProvider('app').getImmediate();\n    const authProvider = container.getProvider('auth-internal');\n    const appCheckProvider = container.getProvider('app-check-internal');\n    return new FirebaseStorageImpl(app, authProvider, appCheckProvider, url, _firebase_app__WEBPACK_IMPORTED_MODULE_0__.SDK_VERSION);\n}\nfunction registerStorage() {\n    (0,_firebase_app__WEBPACK_IMPORTED_MODULE_0__._registerComponent)(new _firebase_component__WEBPACK_IMPORTED_MODULE_2__.Component(STORAGE_TYPE, factory, \"PUBLIC\" /* ComponentType.PUBLIC */).setMultipleInstances(true));\n    (0,_firebase_app__WEBPACK_IMPORTED_MODULE_0__.registerVersion)(name, version);\n}\nregisterStorage();\n\n\n//# sourceMappingURL=index.node.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@firebase+storage@0.13.13_@firebase+app@0.13.1/node_modules/@firebase/storage/dist/node-esm/index.node.esm.js\n");

/***/ })

};
;