"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@firebase+messaging@0.12.21_@firebase+app@0.13.1";
exports.ids = ["vendor-chunks/@firebase+messaging@0.12.21_@firebase+app@0.13.1"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@firebase+messaging@0.12.21_@firebase+app@0.13.1/node_modules/@firebase/messaging/dist/esm/index.esm2017.js":
/*!********************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@firebase+messaging@0.12.21_@firebase+app@0.13.1/node_modules/@firebase/messaging/dist/esm/index.esm2017.js ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deleteToken: () => (/* binding */ deleteToken),\n/* harmony export */   getMessaging: () => (/* binding */ getMessagingInWindow),\n/* harmony export */   getToken: () => (/* binding */ getToken),\n/* harmony export */   isSupported: () => (/* binding */ isWindowSupported),\n/* harmony export */   onMessage: () => (/* binding */ onMessage)\n/* harmony export */ });\n/* harmony import */ var _firebase_installations__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @firebase/installations */ \"(ssr)/../../node_modules/.pnpm/@firebase+installations@0.6.17_@firebase+app@0.13.1/node_modules/@firebase/installations/dist/esm/index.esm2017.js\");\n/* harmony import */ var _firebase_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @firebase/component */ \"(ssr)/../../node_modules/.pnpm/@firebase+component@0.6.17/node_modules/@firebase/component/dist/esm/index.esm2017.js\");\n/* harmony import */ var idb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! idb */ \"(ssr)/../../node_modules/.pnpm/idb@7.1.1/node_modules/idb/build/index.js\");\n/* harmony import */ var _firebase_util__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @firebase/util */ \"(ssr)/../../node_modules/.pnpm/@firebase+util@1.12.0/node_modules/@firebase/util/dist/node-esm/index.node.esm.js\");\n/* harmony import */ var _firebase_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @firebase/app */ \"(ssr)/../../node_modules/.pnpm/@firebase+app@0.13.1/node_modules/@firebase/app/dist/esm/index.esm2017.js\");\n\n\n\n\n\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst DEFAULT_SW_PATH = '/firebase-messaging-sw.js';\nconst DEFAULT_SW_SCOPE = '/firebase-cloud-messaging-push-scope';\nconst DEFAULT_VAPID_KEY = 'BDOU99-h67HcA6JeFXHbSNMu7e2yNNu3RzoMj8TM4W88jITfq7ZmPvIM1Iv-4_l2LxQcYwhqby2xGpWwzjfAnG4';\nconst ENDPOINT = 'https://fcmregistrations.googleapis.com/v1';\nconst CONSOLE_CAMPAIGN_ID = 'google.c.a.c_id';\nconst CONSOLE_CAMPAIGN_NAME = 'google.c.a.c_l';\nconst CONSOLE_CAMPAIGN_TIME = 'google.c.a.ts';\n/** Set to '1' if Analytics is enabled for the campaign */\nconst CONSOLE_CAMPAIGN_ANALYTICS_ENABLED = 'google.c.a.e';\nconst DEFAULT_REGISTRATION_TIMEOUT = 10000;\nvar MessageType$1;\n(function (MessageType) {\n    MessageType[MessageType[\"DATA_MESSAGE\"] = 1] = \"DATA_MESSAGE\";\n    MessageType[MessageType[\"DISPLAY_NOTIFICATION\"] = 3] = \"DISPLAY_NOTIFICATION\";\n})(MessageType$1 || (MessageType$1 = {}));\n\n/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n * in compliance with the License. You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under the License\n * is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n * or implied. See the License for the specific language governing permissions and limitations under\n * the License.\n */\nvar MessageType;\n(function (MessageType) {\n    MessageType[\"PUSH_RECEIVED\"] = \"push-received\";\n    MessageType[\"NOTIFICATION_CLICKED\"] = \"notification-clicked\";\n})(MessageType || (MessageType = {}));\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction arrayToBase64(array) {\n    const uint8Array = new Uint8Array(array);\n    const base64String = btoa(String.fromCharCode(...uint8Array));\n    return base64String.replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n}\nfunction base64ToArray(base64String) {\n    const padding = '='.repeat((4 - (base64String.length % 4)) % 4);\n    const base64 = (base64String + padding)\n        .replace(/\\-/g, '+')\n        .replace(/_/g, '/');\n    const rawData = atob(base64);\n    const outputArray = new Uint8Array(rawData.length);\n    for (let i = 0; i < rawData.length; ++i) {\n        outputArray[i] = rawData.charCodeAt(i);\n    }\n    return outputArray;\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst OLD_DB_NAME = 'fcm_token_details_db';\n/**\n * The last DB version of 'fcm_token_details_db' was 4. This is one higher, so that the upgrade\n * callback is called for all versions of the old DB.\n */\nconst OLD_DB_VERSION = 5;\nconst OLD_OBJECT_STORE_NAME = 'fcm_token_object_Store';\nasync function migrateOldDatabase(senderId) {\n    if ('databases' in indexedDB) {\n        // indexedDb.databases() is an IndexedDB v3 API and does not exist in all browsers. TODO: Remove\n        // typecast when it lands in TS types.\n        const databases = await indexedDB.databases();\n        const dbNames = databases.map(db => db.name);\n        if (!dbNames.includes(OLD_DB_NAME)) {\n            // old DB didn't exist, no need to open.\n            return null;\n        }\n    }\n    let tokenDetails = null;\n    const db = await (0,idb__WEBPACK_IMPORTED_MODULE_2__.openDB)(OLD_DB_NAME, OLD_DB_VERSION, {\n        upgrade: async (db, oldVersion, newVersion, upgradeTransaction) => {\n            var _a;\n            if (oldVersion < 2) {\n                // Database too old, skip migration.\n                return;\n            }\n            if (!db.objectStoreNames.contains(OLD_OBJECT_STORE_NAME)) {\n                // Database did not exist. Nothing to do.\n                return;\n            }\n            const objectStore = upgradeTransaction.objectStore(OLD_OBJECT_STORE_NAME);\n            const value = await objectStore.index('fcmSenderId').get(senderId);\n            await objectStore.clear();\n            if (!value) {\n                // No entry in the database, nothing to migrate.\n                return;\n            }\n            if (oldVersion === 2) {\n                const oldDetails = value;\n                if (!oldDetails.auth || !oldDetails.p256dh || !oldDetails.endpoint) {\n                    return;\n                }\n                tokenDetails = {\n                    token: oldDetails.fcmToken,\n                    createTime: (_a = oldDetails.createTime) !== null && _a !== void 0 ? _a : Date.now(),\n                    subscriptionOptions: {\n                        auth: oldDetails.auth,\n                        p256dh: oldDetails.p256dh,\n                        endpoint: oldDetails.endpoint,\n                        swScope: oldDetails.swScope,\n                        vapidKey: typeof oldDetails.vapidKey === 'string'\n                            ? oldDetails.vapidKey\n                            : arrayToBase64(oldDetails.vapidKey)\n                    }\n                };\n            }\n            else if (oldVersion === 3) {\n                const oldDetails = value;\n                tokenDetails = {\n                    token: oldDetails.fcmToken,\n                    createTime: oldDetails.createTime,\n                    subscriptionOptions: {\n                        auth: arrayToBase64(oldDetails.auth),\n                        p256dh: arrayToBase64(oldDetails.p256dh),\n                        endpoint: oldDetails.endpoint,\n                        swScope: oldDetails.swScope,\n                        vapidKey: arrayToBase64(oldDetails.vapidKey)\n                    }\n                };\n            }\n            else if (oldVersion === 4) {\n                const oldDetails = value;\n                tokenDetails = {\n                    token: oldDetails.fcmToken,\n                    createTime: oldDetails.createTime,\n                    subscriptionOptions: {\n                        auth: arrayToBase64(oldDetails.auth),\n                        p256dh: arrayToBase64(oldDetails.p256dh),\n                        endpoint: oldDetails.endpoint,\n                        swScope: oldDetails.swScope,\n                        vapidKey: arrayToBase64(oldDetails.vapidKey)\n                    }\n                };\n            }\n        }\n    });\n    db.close();\n    // Delete all old databases.\n    await (0,idb__WEBPACK_IMPORTED_MODULE_2__.deleteDB)(OLD_DB_NAME);\n    await (0,idb__WEBPACK_IMPORTED_MODULE_2__.deleteDB)('fcm_vapid_details_db');\n    await (0,idb__WEBPACK_IMPORTED_MODULE_2__.deleteDB)('undefined');\n    return checkTokenDetails(tokenDetails) ? tokenDetails : null;\n}\nfunction checkTokenDetails(tokenDetails) {\n    if (!tokenDetails || !tokenDetails.subscriptionOptions) {\n        return false;\n    }\n    const { subscriptionOptions } = tokenDetails;\n    return (typeof tokenDetails.createTime === 'number' &&\n        tokenDetails.createTime > 0 &&\n        typeof tokenDetails.token === 'string' &&\n        tokenDetails.token.length > 0 &&\n        typeof subscriptionOptions.auth === 'string' &&\n        subscriptionOptions.auth.length > 0 &&\n        typeof subscriptionOptions.p256dh === 'string' &&\n        subscriptionOptions.p256dh.length > 0 &&\n        typeof subscriptionOptions.endpoint === 'string' &&\n        subscriptionOptions.endpoint.length > 0 &&\n        typeof subscriptionOptions.swScope === 'string' &&\n        subscriptionOptions.swScope.length > 0 &&\n        typeof subscriptionOptions.vapidKey === 'string' &&\n        subscriptionOptions.vapidKey.length > 0);\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Exported for tests.\nconst DATABASE_NAME = 'firebase-messaging-database';\nconst DATABASE_VERSION = 1;\nconst OBJECT_STORE_NAME = 'firebase-messaging-store';\nlet dbPromise = null;\nfunction getDbPromise() {\n    if (!dbPromise) {\n        dbPromise = (0,idb__WEBPACK_IMPORTED_MODULE_2__.openDB)(DATABASE_NAME, DATABASE_VERSION, {\n            upgrade: (upgradeDb, oldVersion) => {\n                // We don't use 'break' in this switch statement, the fall-through behavior is what we want,\n                // because if there are multiple versions between the old version and the current version, we\n                // want ALL the migrations that correspond to those versions to run, not only the last one.\n                // eslint-disable-next-line default-case\n                switch (oldVersion) {\n                    case 0:\n                        upgradeDb.createObjectStore(OBJECT_STORE_NAME);\n                }\n            }\n        });\n    }\n    return dbPromise;\n}\n/** Gets record(s) from the objectStore that match the given key. */\nasync function dbGet(firebaseDependencies) {\n    const key = getKey(firebaseDependencies);\n    const db = await getDbPromise();\n    const tokenDetails = (await db\n        .transaction(OBJECT_STORE_NAME)\n        .objectStore(OBJECT_STORE_NAME)\n        .get(key));\n    if (tokenDetails) {\n        return tokenDetails;\n    }\n    else {\n        // Check if there is a tokenDetails object in the old DB.\n        const oldTokenDetails = await migrateOldDatabase(firebaseDependencies.appConfig.senderId);\n        if (oldTokenDetails) {\n            await dbSet(firebaseDependencies, oldTokenDetails);\n            return oldTokenDetails;\n        }\n    }\n}\n/** Assigns or overwrites the record for the given key with the given value. */\nasync function dbSet(firebaseDependencies, tokenDetails) {\n    const key = getKey(firebaseDependencies);\n    const db = await getDbPromise();\n    const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n    await tx.objectStore(OBJECT_STORE_NAME).put(tokenDetails, key);\n    await tx.done;\n    return tokenDetails;\n}\n/** Removes record(s) from the objectStore that match the given key. */\nasync function dbRemove(firebaseDependencies) {\n    const key = getKey(firebaseDependencies);\n    const db = await getDbPromise();\n    const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n    await tx.objectStore(OBJECT_STORE_NAME).delete(key);\n    await tx.done;\n}\nfunction getKey({ appConfig }) {\n    return appConfig.appId;\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst ERROR_MAP = {\n    [\"missing-app-config-values\" /* ErrorCode.MISSING_APP_CONFIG_VALUES */]: 'Missing App configuration value: \"{$valueName}\"',\n    [\"only-available-in-window\" /* ErrorCode.AVAILABLE_IN_WINDOW */]: 'This method is available in a Window context.',\n    [\"only-available-in-sw\" /* ErrorCode.AVAILABLE_IN_SW */]: 'This method is available in a service worker context.',\n    [\"permission-default\" /* ErrorCode.PERMISSION_DEFAULT */]: 'The notification permission was not granted and dismissed instead.',\n    [\"permission-blocked\" /* ErrorCode.PERMISSION_BLOCKED */]: 'The notification permission was not granted and blocked instead.',\n    [\"unsupported-browser\" /* ErrorCode.UNSUPPORTED_BROWSER */]: \"This browser doesn't support the API's required to use the Firebase SDK.\",\n    [\"indexed-db-unsupported\" /* ErrorCode.INDEXED_DB_UNSUPPORTED */]: \"This browser doesn't support indexedDb.open() (ex. Safari iFrame, Firefox Private Browsing, etc)\",\n    [\"failed-service-worker-registration\" /* ErrorCode.FAILED_DEFAULT_REGISTRATION */]: 'We are unable to register the default service worker. {$browserErrorMessage}',\n    [\"token-subscribe-failed\" /* ErrorCode.TOKEN_SUBSCRIBE_FAILED */]: 'A problem occurred while subscribing the user to FCM: {$errorInfo}',\n    [\"token-subscribe-no-token\" /* ErrorCode.TOKEN_SUBSCRIBE_NO_TOKEN */]: 'FCM returned no token when subscribing the user to push.',\n    [\"token-unsubscribe-failed\" /* ErrorCode.TOKEN_UNSUBSCRIBE_FAILED */]: 'A problem occurred while unsubscribing the ' +\n        'user from FCM: {$errorInfo}',\n    [\"token-update-failed\" /* ErrorCode.TOKEN_UPDATE_FAILED */]: 'A problem occurred while updating the user from FCM: {$errorInfo}',\n    [\"token-update-no-token\" /* ErrorCode.TOKEN_UPDATE_NO_TOKEN */]: 'FCM returned no token when updating the user to push.',\n    [\"use-sw-after-get-token\" /* ErrorCode.USE_SW_AFTER_GET_TOKEN */]: 'The useServiceWorker() method may only be called once and must be ' +\n        'called before calling getToken() to ensure your service worker is used.',\n    [\"invalid-sw-registration\" /* ErrorCode.INVALID_SW_REGISTRATION */]: 'The input to useServiceWorker() must be a ServiceWorkerRegistration.',\n    [\"invalid-bg-handler\" /* ErrorCode.INVALID_BG_HANDLER */]: 'The input to setBackgroundMessageHandler() must be a function.',\n    [\"invalid-vapid-key\" /* ErrorCode.INVALID_VAPID_KEY */]: 'The public VAPID key must be a string.',\n    [\"use-vapid-key-after-get-token\" /* ErrorCode.USE_VAPID_KEY_AFTER_GET_TOKEN */]: 'The usePublicVapidKey() method may only be called once and must be ' +\n        'called before calling getToken() to ensure your VAPID key is used.'\n};\nconst ERROR_FACTORY = new _firebase_util__WEBPACK_IMPORTED_MODULE_3__.ErrorFactory('messaging', 'Messaging', ERROR_MAP);\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function requestGetToken(firebaseDependencies, subscriptionOptions) {\n    const headers = await getHeaders(firebaseDependencies);\n    const body = getBody(subscriptionOptions);\n    const subscribeOptions = {\n        method: 'POST',\n        headers,\n        body: JSON.stringify(body)\n    };\n    let responseData;\n    try {\n        const response = await fetch(getEndpoint(firebaseDependencies.appConfig), subscribeOptions);\n        responseData = await response.json();\n    }\n    catch (err) {\n        throw ERROR_FACTORY.create(\"token-subscribe-failed\" /* ErrorCode.TOKEN_SUBSCRIBE_FAILED */, {\n            errorInfo: err === null || err === void 0 ? void 0 : err.toString()\n        });\n    }\n    if (responseData.error) {\n        const message = responseData.error.message;\n        throw ERROR_FACTORY.create(\"token-subscribe-failed\" /* ErrorCode.TOKEN_SUBSCRIBE_FAILED */, {\n            errorInfo: message\n        });\n    }\n    if (!responseData.token) {\n        throw ERROR_FACTORY.create(\"token-subscribe-no-token\" /* ErrorCode.TOKEN_SUBSCRIBE_NO_TOKEN */);\n    }\n    return responseData.token;\n}\nasync function requestUpdateToken(firebaseDependencies, tokenDetails) {\n    const headers = await getHeaders(firebaseDependencies);\n    const body = getBody(tokenDetails.subscriptionOptions);\n    const updateOptions = {\n        method: 'PATCH',\n        headers,\n        body: JSON.stringify(body)\n    };\n    let responseData;\n    try {\n        const response = await fetch(`${getEndpoint(firebaseDependencies.appConfig)}/${tokenDetails.token}`, updateOptions);\n        responseData = await response.json();\n    }\n    catch (err) {\n        throw ERROR_FACTORY.create(\"token-update-failed\" /* ErrorCode.TOKEN_UPDATE_FAILED */, {\n            errorInfo: err === null || err === void 0 ? void 0 : err.toString()\n        });\n    }\n    if (responseData.error) {\n        const message = responseData.error.message;\n        throw ERROR_FACTORY.create(\"token-update-failed\" /* ErrorCode.TOKEN_UPDATE_FAILED */, {\n            errorInfo: message\n        });\n    }\n    if (!responseData.token) {\n        throw ERROR_FACTORY.create(\"token-update-no-token\" /* ErrorCode.TOKEN_UPDATE_NO_TOKEN */);\n    }\n    return responseData.token;\n}\nasync function requestDeleteToken(firebaseDependencies, token) {\n    const headers = await getHeaders(firebaseDependencies);\n    const unsubscribeOptions = {\n        method: 'DELETE',\n        headers\n    };\n    try {\n        const response = await fetch(`${getEndpoint(firebaseDependencies.appConfig)}/${token}`, unsubscribeOptions);\n        const responseData = await response.json();\n        if (responseData.error) {\n            const message = responseData.error.message;\n            throw ERROR_FACTORY.create(\"token-unsubscribe-failed\" /* ErrorCode.TOKEN_UNSUBSCRIBE_FAILED */, {\n                errorInfo: message\n            });\n        }\n    }\n    catch (err) {\n        throw ERROR_FACTORY.create(\"token-unsubscribe-failed\" /* ErrorCode.TOKEN_UNSUBSCRIBE_FAILED */, {\n            errorInfo: err === null || err === void 0 ? void 0 : err.toString()\n        });\n    }\n}\nfunction getEndpoint({ projectId }) {\n    return `${ENDPOINT}/projects/${projectId}/registrations`;\n}\nasync function getHeaders({ appConfig, installations }) {\n    const authToken = await installations.getToken();\n    return new Headers({\n        'Content-Type': 'application/json',\n        Accept: 'application/json',\n        'x-goog-api-key': appConfig.apiKey,\n        'x-goog-firebase-installations-auth': `FIS ${authToken}`\n    });\n}\nfunction getBody({ p256dh, auth, endpoint, vapidKey }) {\n    const body = {\n        web: {\n            endpoint,\n            auth,\n            p256dh\n        }\n    };\n    if (vapidKey !== DEFAULT_VAPID_KEY) {\n        body.web.applicationPubKey = vapidKey;\n    }\n    return body;\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// UpdateRegistration will be called once every week.\nconst TOKEN_EXPIRATION_MS = 7 * 24 * 60 * 60 * 1000; // 7 days\nasync function getTokenInternal(messaging) {\n    const pushSubscription = await getPushSubscription(messaging.swRegistration, messaging.vapidKey);\n    const subscriptionOptions = {\n        vapidKey: messaging.vapidKey,\n        swScope: messaging.swRegistration.scope,\n        endpoint: pushSubscription.endpoint,\n        auth: arrayToBase64(pushSubscription.getKey('auth')),\n        p256dh: arrayToBase64(pushSubscription.getKey('p256dh'))\n    };\n    const tokenDetails = await dbGet(messaging.firebaseDependencies);\n    if (!tokenDetails) {\n        // No token, get a new one.\n        return getNewToken(messaging.firebaseDependencies, subscriptionOptions);\n    }\n    else if (!isTokenValid(tokenDetails.subscriptionOptions, subscriptionOptions)) {\n        // Invalid token, get a new one.\n        try {\n            await requestDeleteToken(messaging.firebaseDependencies, tokenDetails.token);\n        }\n        catch (e) {\n            // Suppress errors because of #2364\n            console.warn(e);\n        }\n        return getNewToken(messaging.firebaseDependencies, subscriptionOptions);\n    }\n    else if (Date.now() >= tokenDetails.createTime + TOKEN_EXPIRATION_MS) {\n        // Weekly token refresh\n        return updateToken(messaging, {\n            token: tokenDetails.token,\n            createTime: Date.now(),\n            subscriptionOptions\n        });\n    }\n    else {\n        // Valid token, nothing to do.\n        return tokenDetails.token;\n    }\n}\n/**\n * This method deletes the token from the database, unsubscribes the token from FCM, and unregisters\n * the push subscription if it exists.\n */\nasync function deleteTokenInternal(messaging) {\n    const tokenDetails = await dbGet(messaging.firebaseDependencies);\n    if (tokenDetails) {\n        await requestDeleteToken(messaging.firebaseDependencies, tokenDetails.token);\n        await dbRemove(messaging.firebaseDependencies);\n    }\n    // Unsubscribe from the push subscription.\n    const pushSubscription = await messaging.swRegistration.pushManager.getSubscription();\n    if (pushSubscription) {\n        return pushSubscription.unsubscribe();\n    }\n    // If there's no SW, consider it a success.\n    return true;\n}\nasync function updateToken(messaging, tokenDetails) {\n    try {\n        const updatedToken = await requestUpdateToken(messaging.firebaseDependencies, tokenDetails);\n        const updatedTokenDetails = Object.assign(Object.assign({}, tokenDetails), { token: updatedToken, createTime: Date.now() });\n        await dbSet(messaging.firebaseDependencies, updatedTokenDetails);\n        return updatedToken;\n    }\n    catch (e) {\n        throw e;\n    }\n}\nasync function getNewToken(firebaseDependencies, subscriptionOptions) {\n    const token = await requestGetToken(firebaseDependencies, subscriptionOptions);\n    const tokenDetails = {\n        token,\n        createTime: Date.now(),\n        subscriptionOptions\n    };\n    await dbSet(firebaseDependencies, tokenDetails);\n    return tokenDetails.token;\n}\n/**\n * Gets a PushSubscription for the current user.\n */\nasync function getPushSubscription(swRegistration, vapidKey) {\n    const subscription = await swRegistration.pushManager.getSubscription();\n    if (subscription) {\n        return subscription;\n    }\n    return swRegistration.pushManager.subscribe({\n        userVisibleOnly: true,\n        // Chrome <= 75 doesn't support base64-encoded VAPID key. For backward compatibility, VAPID key\n        // submitted to pushManager#subscribe must be of type Uint8Array.\n        applicationServerKey: base64ToArray(vapidKey)\n    });\n}\n/**\n * Checks if the saved tokenDetails object matches the configuration provided.\n */\nfunction isTokenValid(dbOptions, currentOptions) {\n    const isVapidKeyEqual = currentOptions.vapidKey === dbOptions.vapidKey;\n    const isEndpointEqual = currentOptions.endpoint === dbOptions.endpoint;\n    const isAuthEqual = currentOptions.auth === dbOptions.auth;\n    const isP256dhEqual = currentOptions.p256dh === dbOptions.p256dh;\n    return isVapidKeyEqual && isEndpointEqual && isAuthEqual && isP256dhEqual;\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction externalizePayload(internalPayload) {\n    const payload = {\n        from: internalPayload.from,\n        // eslint-disable-next-line camelcase\n        collapseKey: internalPayload.collapse_key,\n        // eslint-disable-next-line camelcase\n        messageId: internalPayload.fcmMessageId\n    };\n    propagateNotificationPayload(payload, internalPayload);\n    propagateDataPayload(payload, internalPayload);\n    propagateFcmOptions(payload, internalPayload);\n    return payload;\n}\nfunction propagateNotificationPayload(payload, messagePayloadInternal) {\n    if (!messagePayloadInternal.notification) {\n        return;\n    }\n    payload.notification = {};\n    const title = messagePayloadInternal.notification.title;\n    if (!!title) {\n        payload.notification.title = title;\n    }\n    const body = messagePayloadInternal.notification.body;\n    if (!!body) {\n        payload.notification.body = body;\n    }\n    const image = messagePayloadInternal.notification.image;\n    if (!!image) {\n        payload.notification.image = image;\n    }\n    const icon = messagePayloadInternal.notification.icon;\n    if (!!icon) {\n        payload.notification.icon = icon;\n    }\n}\nfunction propagateDataPayload(payload, messagePayloadInternal) {\n    if (!messagePayloadInternal.data) {\n        return;\n    }\n    payload.data = messagePayloadInternal.data;\n}\nfunction propagateFcmOptions(payload, messagePayloadInternal) {\n    var _a, _b, _c, _d, _e;\n    // fcmOptions.link value is written into notification.click_action. see more in b/232072111\n    if (!messagePayloadInternal.fcmOptions &&\n        !((_a = messagePayloadInternal.notification) === null || _a === void 0 ? void 0 : _a.click_action)) {\n        return;\n    }\n    payload.fcmOptions = {};\n    const link = (_c = (_b = messagePayloadInternal.fcmOptions) === null || _b === void 0 ? void 0 : _b.link) !== null && _c !== void 0 ? _c : (_d = messagePayloadInternal.notification) === null || _d === void 0 ? void 0 : _d.click_action;\n    if (!!link) {\n        payload.fcmOptions.link = link;\n    }\n    // eslint-disable-next-line camelcase\n    const analyticsLabel = (_e = messagePayloadInternal.fcmOptions) === null || _e === void 0 ? void 0 : _e.analytics_label;\n    if (!!analyticsLabel) {\n        payload.fcmOptions.analyticsLabel = analyticsLabel;\n    }\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction isConsoleMessage(data) {\n    // This message has a campaign ID, meaning it was sent using the Firebase Console.\n    return typeof data === 'object' && !!data && CONSOLE_CAMPAIGN_ID in data;\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n_mergeStrings('AzSCbw63g1R0nCw85jG8', 'Iaya3yLKwmgvh7cF0q4');\nfunction _mergeStrings(s1, s2) {\n    const resultArray = [];\n    for (let i = 0; i < s1.length; i++) {\n        resultArray.push(s1.charAt(i));\n        if (i < s2.length) {\n            resultArray.push(s2.charAt(i));\n        }\n    }\n    return resultArray.join('');\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction extractAppConfig(app) {\n    if (!app || !app.options) {\n        throw getMissingValueError('App Configuration Object');\n    }\n    if (!app.name) {\n        throw getMissingValueError('App Name');\n    }\n    // Required app config keys\n    const configKeys = [\n        'projectId',\n        'apiKey',\n        'appId',\n        'messagingSenderId'\n    ];\n    const { options } = app;\n    for (const keyName of configKeys) {\n        if (!options[keyName]) {\n            throw getMissingValueError(keyName);\n        }\n    }\n    return {\n        appName: app.name,\n        projectId: options.projectId,\n        apiKey: options.apiKey,\n        appId: options.appId,\n        senderId: options.messagingSenderId\n    };\n}\nfunction getMissingValueError(valueName) {\n    return ERROR_FACTORY.create(\"missing-app-config-values\" /* ErrorCode.MISSING_APP_CONFIG_VALUES */, {\n        valueName\n    });\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nclass MessagingService {\n    constructor(app, installations, analyticsProvider) {\n        // logging is only done with end user consent. Default to false.\n        this.deliveryMetricsExportedToBigQueryEnabled = false;\n        this.onBackgroundMessageHandler = null;\n        this.onMessageHandler = null;\n        this.logEvents = [];\n        this.isLogServiceStarted = false;\n        const appConfig = extractAppConfig(app);\n        this.firebaseDependencies = {\n            app,\n            appConfig,\n            installations,\n            analyticsProvider\n        };\n    }\n    _delete() {\n        return Promise.resolve();\n    }\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function registerDefaultSw(messaging) {\n    try {\n        messaging.swRegistration = await navigator.serviceWorker.register(DEFAULT_SW_PATH, {\n            scope: DEFAULT_SW_SCOPE\n        });\n        // The timing when browser updates sw when sw has an update is unreliable from experiment. It\n        // leads to version conflict when the SDK upgrades to a newer version in the main page, but sw\n        // is stuck with the old version. For example,\n        // https://github.com/firebase/firebase-js-sdk/issues/2590 The following line reliably updates\n        // sw if there was an update.\n        messaging.swRegistration.update().catch(() => {\n            /* it is non blocking and we don't care if it failed */\n        });\n        await waitForRegistrationActive(messaging.swRegistration);\n    }\n    catch (e) {\n        throw ERROR_FACTORY.create(\"failed-service-worker-registration\" /* ErrorCode.FAILED_DEFAULT_REGISTRATION */, {\n            browserErrorMessage: e === null || e === void 0 ? void 0 : e.message\n        });\n    }\n}\n/**\n * Waits for registration to become active. MDN documentation claims that\n * a service worker registration should be ready to use after awaiting\n * navigator.serviceWorker.register() but that doesn't seem to be the case in\n * practice, causing the SDK to throw errors when calling\n * swRegistration.pushManager.subscribe() too soon after register(). The only\n * solution seems to be waiting for the service worker registration `state`\n * to become \"active\".\n */\nasync function waitForRegistrationActive(registration) {\n    return new Promise((resolve, reject) => {\n        const rejectTimeout = setTimeout(() => reject(new Error(`Service worker not registered after ${DEFAULT_REGISTRATION_TIMEOUT} ms`)), DEFAULT_REGISTRATION_TIMEOUT);\n        const incomingSw = registration.installing || registration.waiting;\n        if (registration.active) {\n            clearTimeout(rejectTimeout);\n            resolve();\n        }\n        else if (incomingSw) {\n            incomingSw.onstatechange = ev => {\n                var _a;\n                if (((_a = ev.target) === null || _a === void 0 ? void 0 : _a.state) === 'activated') {\n                    incomingSw.onstatechange = null;\n                    clearTimeout(rejectTimeout);\n                    resolve();\n                }\n            };\n        }\n        else {\n            clearTimeout(rejectTimeout);\n            reject(new Error('No incoming service worker found.'));\n        }\n    });\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function updateSwReg(messaging, swRegistration) {\n    if (!swRegistration && !messaging.swRegistration) {\n        await registerDefaultSw(messaging);\n    }\n    if (!swRegistration && !!messaging.swRegistration) {\n        return;\n    }\n    if (!(swRegistration instanceof ServiceWorkerRegistration)) {\n        throw ERROR_FACTORY.create(\"invalid-sw-registration\" /* ErrorCode.INVALID_SW_REGISTRATION */);\n    }\n    messaging.swRegistration = swRegistration;\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function updateVapidKey(messaging, vapidKey) {\n    if (!!vapidKey) {\n        messaging.vapidKey = vapidKey;\n    }\n    else if (!messaging.vapidKey) {\n        messaging.vapidKey = DEFAULT_VAPID_KEY;\n    }\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function getToken$1(messaging, options) {\n    if (!navigator) {\n        throw ERROR_FACTORY.create(\"only-available-in-window\" /* ErrorCode.AVAILABLE_IN_WINDOW */);\n    }\n    if (Notification.permission === 'default') {\n        await Notification.requestPermission();\n    }\n    if (Notification.permission !== 'granted') {\n        throw ERROR_FACTORY.create(\"permission-blocked\" /* ErrorCode.PERMISSION_BLOCKED */);\n    }\n    await updateVapidKey(messaging, options === null || options === void 0 ? void 0 : options.vapidKey);\n    await updateSwReg(messaging, options === null || options === void 0 ? void 0 : options.serviceWorkerRegistration);\n    return getTokenInternal(messaging);\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function logToScion(messaging, messageType, data) {\n    const eventType = getEventType(messageType);\n    const analytics = await messaging.firebaseDependencies.analyticsProvider.get();\n    analytics.logEvent(eventType, {\n        /* eslint-disable camelcase */\n        message_id: data[CONSOLE_CAMPAIGN_ID],\n        message_name: data[CONSOLE_CAMPAIGN_NAME],\n        message_time: data[CONSOLE_CAMPAIGN_TIME],\n        message_device_time: Math.floor(Date.now() / 1000)\n        /* eslint-enable camelcase */\n    });\n}\nfunction getEventType(messageType) {\n    switch (messageType) {\n        case MessageType.NOTIFICATION_CLICKED:\n            return 'notification_open';\n        case MessageType.PUSH_RECEIVED:\n            return 'notification_foreground';\n        default:\n            throw new Error();\n    }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function messageEventListener(messaging, event) {\n    const internalPayload = event.data;\n    if (!internalPayload.isFirebaseMessaging) {\n        return;\n    }\n    if (messaging.onMessageHandler &&\n        internalPayload.messageType === MessageType.PUSH_RECEIVED) {\n        if (typeof messaging.onMessageHandler === 'function') {\n            messaging.onMessageHandler(externalizePayload(internalPayload));\n        }\n        else {\n            messaging.onMessageHandler.next(externalizePayload(internalPayload));\n        }\n    }\n    // Log to Scion if applicable\n    const dataPayload = internalPayload.data;\n    if (isConsoleMessage(dataPayload) &&\n        dataPayload[CONSOLE_CAMPAIGN_ANALYTICS_ENABLED] === '1') {\n        await logToScion(messaging, internalPayload.messageType, dataPayload);\n    }\n}\n\nconst name = \"@firebase/messaging\";\nconst version = \"0.12.21\";\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst WindowMessagingFactory = (container) => {\n    const messaging = new MessagingService(container.getProvider('app').getImmediate(), container.getProvider('installations-internal').getImmediate(), container.getProvider('analytics-internal'));\n    navigator.serviceWorker.addEventListener('message', e => messageEventListener(messaging, e));\n    return messaging;\n};\nconst WindowMessagingInternalFactory = (container) => {\n    const messaging = container\n        .getProvider('messaging')\n        .getImmediate();\n    const messagingInternal = {\n        getToken: (options) => getToken$1(messaging, options)\n    };\n    return messagingInternal;\n};\nfunction registerMessagingInWindow() {\n    (0,_firebase_app__WEBPACK_IMPORTED_MODULE_4__._registerComponent)(new _firebase_component__WEBPACK_IMPORTED_MODULE_1__.Component('messaging', WindowMessagingFactory, \"PUBLIC\" /* ComponentType.PUBLIC */));\n    (0,_firebase_app__WEBPACK_IMPORTED_MODULE_4__._registerComponent)(new _firebase_component__WEBPACK_IMPORTED_MODULE_1__.Component('messaging-internal', WindowMessagingInternalFactory, \"PRIVATE\" /* ComponentType.PRIVATE */));\n    (0,_firebase_app__WEBPACK_IMPORTED_MODULE_4__.registerVersion)(name, version);\n    // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n    (0,_firebase_app__WEBPACK_IMPORTED_MODULE_4__.registerVersion)(name, version, 'esm2017');\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Checks if all required APIs exist in the browser.\n * @returns a Promise that resolves to a boolean.\n *\n * @public\n */\nasync function isWindowSupported() {\n    try {\n        // This throws if open() is unsupported, so adding it to the conditional\n        // statement below can cause an uncaught error.\n        await (0,_firebase_util__WEBPACK_IMPORTED_MODULE_3__.validateIndexedDBOpenable)();\n    }\n    catch (e) {\n        return false;\n    }\n    // firebase-js-sdk/issues/2393 reveals that idb#open in Safari iframe and Firefox private browsing\n    // might be prohibited to run. In these contexts, an error would be thrown during the messaging\n    // instantiating phase, informing the developers to import/call isSupported for special handling.\n    return (typeof window !== 'undefined' &&\n        (0,_firebase_util__WEBPACK_IMPORTED_MODULE_3__.isIndexedDBAvailable)() &&\n        (0,_firebase_util__WEBPACK_IMPORTED_MODULE_3__.areCookiesEnabled)() &&\n        'serviceWorker' in navigator &&\n        'PushManager' in window &&\n        'Notification' in window &&\n        'fetch' in window &&\n        ServiceWorkerRegistration.prototype.hasOwnProperty('showNotification') &&\n        PushSubscription.prototype.hasOwnProperty('getKey'));\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function deleteToken$1(messaging) {\n    if (!navigator) {\n        throw ERROR_FACTORY.create(\"only-available-in-window\" /* ErrorCode.AVAILABLE_IN_WINDOW */);\n    }\n    if (!messaging.swRegistration) {\n        await registerDefaultSw(messaging);\n    }\n    return deleteTokenInternal(messaging);\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction onMessage$1(messaging, nextOrObserver) {\n    if (!navigator) {\n        throw ERROR_FACTORY.create(\"only-available-in-window\" /* ErrorCode.AVAILABLE_IN_WINDOW */);\n    }\n    messaging.onMessageHandler = nextOrObserver;\n    return () => {\n        messaging.onMessageHandler = null;\n    };\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Retrieves a Firebase Cloud Messaging instance.\n *\n * @returns The Firebase Cloud Messaging instance associated with the provided firebase app.\n *\n * @public\n */\nfunction getMessagingInWindow(app = (0,_firebase_app__WEBPACK_IMPORTED_MODULE_4__.getApp)()) {\n    // Conscious decision to make this async check non-blocking during the messaging instance\n    // initialization phase for performance consideration. An error would be thrown latter for\n    // developer's information. Developers can then choose to import and call `isSupported` for\n    // special handling.\n    isWindowSupported().then(isSupported => {\n        // If `isWindowSupported()` resolved, but returned false.\n        if (!isSupported) {\n            throw ERROR_FACTORY.create(\"unsupported-browser\" /* ErrorCode.UNSUPPORTED_BROWSER */);\n        }\n    }, _ => {\n        // If `isWindowSupported()` rejected.\n        throw ERROR_FACTORY.create(\"indexed-db-unsupported\" /* ErrorCode.INDEXED_DB_UNSUPPORTED */);\n    });\n    return (0,_firebase_app__WEBPACK_IMPORTED_MODULE_4__._getProvider)((0,_firebase_util__WEBPACK_IMPORTED_MODULE_3__.getModularInstance)(app), 'messaging').getImmediate();\n}\n/**\n * Subscribes the {@link Messaging} instance to push notifications. Returns a Firebase Cloud\n * Messaging registration token that can be used to send push messages to that {@link Messaging}\n * instance.\n *\n * If notification permission isn't already granted, this method asks the user for permission. The\n * returned promise rejects if the user does not allow the app to show notifications.\n *\n * @param messaging - The {@link Messaging} instance.\n * @param options - Provides an optional vapid key and an optional service worker registration.\n *\n * @returns The promise resolves with an FCM registration token.\n *\n * @public\n */\nasync function getToken(messaging, options) {\n    messaging = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_3__.getModularInstance)(messaging);\n    return getToken$1(messaging, options);\n}\n/**\n * Deletes the registration token associated with this {@link Messaging} instance and unsubscribes\n * the {@link Messaging} instance from the push subscription.\n *\n * @param messaging - The {@link Messaging} instance.\n *\n * @returns The promise resolves when the token has been successfully deleted.\n *\n * @public\n */\nfunction deleteToken(messaging) {\n    messaging = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_3__.getModularInstance)(messaging);\n    return deleteToken$1(messaging);\n}\n/**\n * When a push message is received and the user is currently on a page for your origin, the\n * message is passed to the page and an `onMessage()` event is dispatched with the payload of\n * the push message.\n *\n *\n * @param messaging - The {@link Messaging} instance.\n * @param nextOrObserver - This function, or observer object with `next` defined,\n *     is called when a message is received and the user is currently viewing your page.\n * @returns To stop listening for messages execute this returned function.\n *\n * @public\n */\nfunction onMessage(messaging, nextOrObserver) {\n    messaging = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_3__.getModularInstance)(messaging);\n    return onMessage$1(messaging, nextOrObserver);\n}\n\n/**\n * The Firebase Cloud Messaging Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\nregisterMessagingInWindow();\n\n\n//# sourceMappingURL=index.esm2017.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@firebase+messaging@0.12.21_@firebase+app@0.13.1/node_modules/@firebase/messaging/dist/esm/index.esm2017.js\n");

/***/ })

};
;