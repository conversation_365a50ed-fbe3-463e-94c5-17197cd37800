import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useCart } from '../contexts/CartContext';

interface CustomFooterNavProps {
  state: any;
  descriptors: any;
  navigation: any;
}

const { width } = Dimensions.get('window');

export default function CustomFooterNav({ state, descriptors, navigation }: CustomFooterNavProps) {
  const { getCartItemCount } = useCart();
  const cartItemCount = getCartItemCount();
  const insets = useSafeAreaInsets();

  const getIconName = (routeName: string, focused: boolean) => {
    switch (routeName) {
      case 'Home':
        return focused ? 'home' : 'home-outline';
      case 'Restaurants':
        return focused ? 'storefront' : 'storefront-outline';
      case 'Cart':
        return focused ? 'bag' : 'bag-outline';
      case 'Search':
        return focused ? 'search' : 'search-outline';
      case 'Profile':
        return focused ? 'person' : 'person-outline';
      default:
        return 'home-outline';
    }
  };

  const getLabel = (routeName: string) => {
    switch (routeName) {
      case 'Home':
        return 'Home';
      case 'Restaurants':
        return 'Stores';
      case 'Cart':
        return 'Cart';
      case 'Search':
        return 'Search';
      case 'Profile':
        return 'Account';
      default:
        return routeName;
    }
  };

  return (
    <View style={[styles.container, { paddingBottom: insets.bottom }]}>
      <View style={styles.tabBar}>
        {state.routes.map((route: any, index: number) => {
          const { options } = descriptors[route.key];
          const label = getLabel(route.name);
          const isFocused = state.index === index;
          const isCartTab = route.name === 'Cart';

          const onPress = () => {
            const event = navigation.emit({
              type: 'tabPress',
              target: route.key,
              canPreventDefault: true,
            });

            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name);
            }
          };

          const onLongPress = () => {
            navigation.emit({
              type: 'tabLongPress',
              target: route.key,
            });
          };

          if (isCartTab) {
            // Special elevated cart button
            return (
              <View key={index} style={styles.cartTabContainer}>
                <TouchableOpacity
                  accessibilityRole="button"
                  accessibilityState={isFocused ? { selected: true } : {}}
                  accessibilityLabel={options.tabBarAccessibilityLabel}
                  testID={options.tabBarTestID}
                  onPress={onPress}
                  onLongPress={onLongPress}
                  style={styles.cartButton}
                >
                  <View style={styles.cartButtonInner}>
                    <Ionicons
                      name={getIconName(route.name, isFocused)}
                      size={24}
                      color={isFocused ? '#374151' : '#6b7280'}
                    />
                    {cartItemCount > 0 && (
                      <View style={styles.badge}>
                        <Text style={styles.badgeText}>
                          {cartItemCount > 99 ? '99+' : cartItemCount}
                        </Text>
                      </View>
                    )}
                  </View>
                  <Text style={[styles.cartLabel, { color: '#9ca3af' }]}>
                    {label}
                  </Text>
                </TouchableOpacity>
              </View>
            );
          }

          // Regular tab buttons
          return (
            <TouchableOpacity
              key={index}
              accessibilityRole="button"
              accessibilityState={isFocused ? { selected: true } : {}}
              accessibilityLabel={options.tabBarAccessibilityLabel}
              testID={options.tabBarTestID}
              onPress={onPress}
              onLongPress={onLongPress}
              style={styles.tab}
            >
              <Ionicons
                name={getIconName(route.name, isFocused)}
                size={20}
                color={isFocused ? '#f97316' : '#9ca3af'}
              />
              <Text style={[
                styles.label,
                { color: isFocused ? '#f97316' : '#9ca3af' }
              ]}>
                {label}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  tabBar: {
    flexDirection: 'row',
    height: 60,
    alignItems: 'center',
    justifyContent: 'space-around',
    paddingTop: 8,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 4,
  },
  cartTabContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: -16, // Elevate the cart button slightly less
  },
  cartButton: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  cartButtonInner: {
    width: 52,
    height: 52,
    borderRadius: 26,
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    position: 'relative',
    borderWidth: 1,
    borderColor: '#f3f4f6',
  },
  badge: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: '#ef4444',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
    borderWidth: 2,
    borderColor: 'white',
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  label: {
    fontSize: 10,
    fontWeight: '500',
    marginTop: 2,
  },
  cartLabel: {
    fontSize: 10,
    fontWeight: '500',
    marginTop: 4,
  },
});
