import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

// Import components
import RestaurantCard from '../components/RestaurantCard';
import CategoryFilter from '../components/CategoryFilter';
import SearchBar from '../components/SearchBar';
import MapSection from '../components/MapSection';
import MobileHeader from '../components/MobileHeader';
import CustomFooterNav from '../components/CustomFooterNav';
import { ResponsiveContainer, ResponsiveCard, ResponsiveText, ResponsiveGrid } from '../components/ResponsiveContainer';
import { useResponsive } from '../utils/responsive';
import { useCart } from '../contexts/CartContext';

// Temporary types until shared-types is working
interface Restaurant {
  id: string;
  name: string;
  description: string;
  imageUrl?: string;
  cuisine: string[];
  rating?: number;
  deliveryTime?: string;
  deliveryFee?: number;
  minimumOrder?: number;
  distance?: number;
  isOpen: boolean;
}

interface Category {
  id: string;
  name: string;
  image?: string;
  description?: string;
  featured?: boolean;
}

export default function HomeScreen() {
  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [showMap, setShowMap] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<{
    lat: number;
    lng: number;
    address: string;
  } | null>(null);

  // Get responsive screen information
  const { isTablet, deviceType } = useResponsive();

  // Cart functionality
  const { addToCart, getCartItemCount } = useCart();
  const cartItemCount = getCartItemCount();

  // Load data function (same logic as web app)
  const loadData = async () => {
    try {
      // TODO: Implement Firebase data loading
      // This will use the same business logic as your web app
      console.log('Loading restaurants and categories...');
      
      // Placeholder data for now
      setRestaurants([]);
      setCategories([]);
    } catch (error) {
      console.error('Error loading data:', error);
      setRestaurants([]);
      setCategories([]);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  // Filter restaurants (same logic as web app)
  const filteredRestaurants = restaurants.filter(restaurant => {
    const matchesSearch = !searchQuery ||
      restaurant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      restaurant.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      restaurant.cuisine.some(c => c.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesCategory = !selectedCategory ||
      restaurant.cuisine.includes(selectedCategory);

    return matchesSearch && matchesCategory;
  });

  // Handle refresh
  const onRefresh = () => {
    setRefreshing(true);
    loadData();
  };

  // Handle location select
  const handleLocationSelect = (location: {lat: number; lng: number; address: string}) => {
    setSelectedLocation(location);
    console.log('Location selected:', location);
  };

  // Demo function to add a test item
  const addTestItem = () => {
    const testItem = {
      id: `test-${Date.now()}`,
      name: 'Delicious Pizza',
      description: 'A mouth-watering pizza to test the cart',
      price: 15.99,
      image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400',
      restaurantId: 'test-restaurant',
      category: 'Pizza',
      available: true,
    };
    addToCart(testItem, 1);
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f9fafb' }}>
      {/* Mobile Header - Now responsive for tablets */}
      <MobileHeader
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        onNotificationPress={() => {
          // Handle notification press
          console.log('Notification pressed');
        }}
        onWishlistPress={() => {
          // Handle wishlist press
          console.log('Wishlist pressed');
        }}
      />

      <ScrollView
        className="flex-1"
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >

        {/* Map Section */}
        <MapSection
          showMap={showMap}
          onToggleMap={() => setShowMap(!showMap)}
          selectedLocation={selectedLocation}
          onLocationSelect={handleLocationSelect}
        />

        {/* Test Footer Navigation */}
        <ResponsiveContainer>
          <ResponsiveCard style={{ backgroundColor: '#f3f4f6', marginBottom: 16 }}>
            <ResponsiveText variant="subtitle" style={{ marginBottom: 12, color: '#374151' }}>
              Test Footer Navigation
            </ResponsiveText>
            <ResponsiveText style={{ color: '#6b7280', marginBottom: 16 }}>
              Cart Items: {cartItemCount}
            </ResponsiveText>
            <TouchableOpacity
              onPress={addTestItem}
              style={{
                backgroundColor: '#f97316',
                paddingHorizontal: isTablet ? 32 : 24,
                paddingVertical: isTablet ? 16 : 12,
                borderRadius: isTablet ? 12 : 8,
                alignItems: 'center',
                marginBottom: 12,
              }}
            >
              <ResponsiveText style={{ color: 'white', fontWeight: '600' }}>
                Add Item to Cart
              </ResponsiveText>
            </TouchableOpacity>
            <ResponsiveText style={{ color: '#6b7280', fontSize: 14, textAlign: 'center' }}>
              Add items to see the cart badge update in the footer navigation!
            </ResponsiveText>
          </ResponsiveCard>
        </ResponsiveContainer>

        {/* Categories */}
        {categories.length > 0 && (
          <CategoryFilter
            categories={categories}
            selectedCategory={selectedCategory}
            onSelectCategory={setSelectedCategory}
          />
        )}

        {/* Restaurants Section */}
        <ResponsiveContainer>
          <ResponsiveCard>
            <ResponsiveText variant="title" style={{ marginBottom: 24 }}>
              {searchQuery || selectedCategory ? 'Search Results' : 'Restaurants'}
            </ResponsiveText>

            {loading ? (
              <View style={{ alignItems: 'center', paddingVertical: 64 }}>
                <ActivityIndicator size="large" color="#f97316" />
                <ResponsiveText style={{ color: '#6b7280', marginTop: 16 }}>
                  Loading restaurants...
                </ResponsiveText>
              </View>
            ) : filteredRestaurants.length > 0 ? (
              isTablet ? (
                <ResponsiveGrid spacing={16} minItemWidth={300}>
                  {filteredRestaurants.map((restaurant) => (
                    <RestaurantCard key={restaurant.id} restaurant={restaurant} />
                  ))}
                </ResponsiveGrid>
              ) : (
                <View style={{ gap: 16 }}>
                  {filteredRestaurants.map((restaurant) => (
                    <RestaurantCard key={restaurant.id} restaurant={restaurant} />
                  ))}
                </View>
              )
            ) : (
              <View style={{ alignItems: 'center', paddingVertical: 64 }}>
                <View style={{
                  width: isTablet ? 96 : 80,
                  height: isTablet ? 96 : 80,
                  backgroundColor: '#e5e7eb',
                  borderRadius: isTablet ? 48 : 40,
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginBottom: 24,
                }}>
                  <Ionicons
                    name="restaurant-outline"
                    size={isTablet ? 56 : 48}
                    color="#9CA3AF"
                  />
                </View>
                <ResponsiveText variant="subtitle" style={{ marginBottom: 8, textAlign: 'center' }}>
                  No restaurants available yet
                </ResponsiveText>
                <ResponsiveText style={{
                  color: '#6b7280',
                  textAlign: 'center',
                  marginBottom: 24,
                  paddingHorizontal: 16,
                }}>
                  {searchQuery || selectedCategory
                    ? 'No restaurants found matching your criteria. Try adjusting your search or filters.'
                    : "We're working on adding amazing restaurants to your area. Check back soon!"
                  }
                </ResponsiveText>
                {(searchQuery || selectedCategory) && (
                  <TouchableOpacity
                    onPress={() => {
                      setSearchQuery('');
                      setSelectedCategory('');
                    }}
                    style={{
                      backgroundColor: '#f97316',
                      paddingHorizontal: isTablet ? 32 : 24,
                      paddingVertical: isTablet ? 16 : 12,
                      borderRadius: isTablet ? 12 : 8,
                    }}
                  >
                    <ResponsiveText style={{ color: 'white', fontWeight: '600' }}>
                      Clear Filters
                    </ResponsiveText>
                  </TouchableOpacity>
                )}
              </View>
            )}
          </ResponsiveCard>
        </ResponsiveContainer>

        {/* Footer */}
        <ResponsiveContainer>
          <ResponsiveCard style={{ backgroundColor: '#111827' }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
              <View style={{
                width: isTablet ? 40 : 32,
                height: isTablet ? 40 : 32,
                backgroundColor: '#f97316',
                borderRadius: isTablet ? 12 : 8,
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: 12,
              }}>
                <ResponsiveText style={{ color: 'white', fontWeight: 'bold' }}>T</ResponsiveText>
              </View>
              <ResponsiveText variant="title" style={{ color: 'white' }}>Tap2Go</ResponsiveText>
            </View>
            <ResponsiveText style={{ color: '#9ca3af' }}>
              Your favorite food delivery platform. Fast, reliable, and delicious.
            </ResponsiveText>
          </ResponsiveCard>
        </ResponsiveContainer>
      </ScrollView>

      {/* Footer Navigation - Simple component, no React Navigation */}
      <View style={{
        flexDirection: 'row',
        backgroundColor: '#FFFFFF',
        borderTopWidth: 1,
        borderTopColor: '#E5E5E5',
        paddingTop: 8,
        paddingBottom: 8,
        height: 60,
      }}>
        {[
          { name: 'Home', icon: 'home', active: true },
          { name: 'Stores', icon: 'storefront-outline', active: false },
          { name: 'Cart', icon: 'bag-outline', active: false },
          { name: 'Search', icon: 'search-outline', active: false },
          { name: 'Account', icon: 'person-outline', active: false },
        ].map((tab, index) => (
          <TouchableOpacity
            key={tab.name}
            style={{
              flex: 1,
              alignItems: 'center',
              justifyContent: 'center',
              paddingVertical: 4,
            }}
            onPress={() => console.log(`${tab.name} pressed`)}
          >
            {tab.name === 'Cart' ? (
              // Special elevated circular design for Cart
              <View style={{
                width: 44,
                height: 44,
                borderRadius: 22,
                backgroundColor: '#FFFFFF',
                alignItems: 'center',
                justifyContent: 'center',
                marginTop: -12, // Elevate above other tabs
                shadowColor: '#000',
                shadowOffset: {
                  width: 0,
                  height: 4,
                },
                shadowOpacity: 0.15,
                shadowRadius: 8,
                elevation: 8, // Android shadow
              }}>
                <Ionicons
                  name="cart-outline"
                  size={22}
                  color="#666"
                />
              </View>
            ) : (
              // Regular design for other tabs
              <Ionicons
                name={tab.icon as any}
                size={20}
                color={tab.active ? '#FF6B35' : '#666'}
              />
            )}
            <Text style={{
              fontSize: 10,
              marginTop: tab.name === 'Cart' ? 6 : 2, // Extra spacing for elevated cart
              fontWeight: '500',
              color: tab.active ? '#FF6B35' : '#666',
            }}>
              {tab.name}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </SafeAreaView>
  );
}


