import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import FooterNavigation from '../components/FooterNavigation';

export default function StoresScreen({ navigation }: any) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');

  const categories = ['All', 'Fast Food', 'Pizza', 'Asian', 'Healthy', 'Desserts'];

  const stores = [
    {
      id: '1',
      name: 'Burger Palace',
      image: 'https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=400',
      rating: 4.8,
      deliveryTime: '15-25 min',
      deliveryFee: 2.99,
      category: 'Fast Food',
      distance: '0.8 km',
      isOpen: true,
    },
    {
      id: '2',
      name: 'Pizza Corner',
      image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400',
      rating: 4.6,
      deliveryTime: '20-30 min',
      deliveryFee: 1.99,
      category: 'Pizza',
      distance: '1.2 km',
      isOpen: true,
    },
    {
      id: '3',
      name: 'Sushi Express',
      image: 'https://images.unsplash.com/photo-1579584425555-c3ce17fd4351?w=400',
      rating: 4.9,
      deliveryTime: '25-35 min',
      deliveryFee: 3.99,
      category: 'Asian',
      distance: '2.1 km',
      isOpen: true,
    },
    {
      id: '4',
      name: 'Green Bowl',
      image: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400',
      rating: 4.7,
      deliveryTime: '18-28 min',
      deliveryFee: 2.49,
      category: 'Healthy',
      distance: '1.5 km',
      isOpen: false,
    },
  ];

  const filteredStores = stores.filter(store => {
    const matchesSearch = store.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'All' || store.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f9fafb' }}>
      {/* Header */}
      <View style={{ 
        flexDirection: 'row', 
        alignItems: 'center', 
        paddingHorizontal: 16, 
        paddingVertical: 12,
        backgroundColor: '#fff',
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
      }}>
        <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#111827', flex: 1 }}>
          Stores
        </Text>
        <TouchableOpacity style={{ padding: 8 }}>
          <Ionicons name="filter-outline" size={24} color="#6b7280" />
        </TouchableOpacity>
      </View>

      <ScrollView style={{ flex: 1 }}>
        {/* Search Bar */}
        <View style={{ padding: 16 }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: '#fff',
            borderRadius: 12,
            paddingHorizontal: 16,
            paddingVertical: 12,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.05,
            shadowRadius: 2,
            elevation: 2,
          }}>
            <Ionicons name="search-outline" size={20} color="#9ca3af" />
            <TextInput
              style={{ flex: 1, marginLeft: 12, fontSize: 16, color: '#111827' }}
              placeholder="Search stores..."
              placeholderTextColor="#9ca3af"
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
          </View>
        </View>

        {/* Categories */}
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          style={{ paddingLeft: 16, marginBottom: 16 }}
        >
          {categories.map((category) => (
            <TouchableOpacity
              key={category}
              onPress={() => setSelectedCategory(category)}
              style={{
                backgroundColor: selectedCategory === category ? '#f97316' : '#fff',
                paddingHorizontal: 20,
                paddingVertical: 10,
                borderRadius: 20,
                marginRight: 12,
                borderWidth: 1,
                borderColor: selectedCategory === category ? '#f97316' : '#e5e7eb',
              }}
            >
              <Text style={{
                color: selectedCategory === category ? '#fff' : '#6b7280',
                fontWeight: '600',
                fontSize: 14,
              }}>
                {category}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>

        {/* Stores List */}
        <View style={{ paddingHorizontal: 16 }}>
          {filteredStores.map((store) => (
            <TouchableOpacity
              key={store.id}
              style={{
                backgroundColor: '#fff',
                borderRadius: 16,
                marginBottom: 16,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.1,
                shadowRadius: 4,
                elevation: 3,
              }}
            >
              <Image
                source={{ uri: store.image }}
                style={{
                  width: '100%',
                  height: 160,
                  borderTopLeftRadius: 16,
                  borderTopRightRadius: 16,
                }}
              />
              <View style={{ padding: 16 }}>
                <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                  <View style={{ flex: 1 }}>
                    <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 4 }}>
                      {store.name}
                    </Text>
                    <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
                      <Ionicons name="star" size={16} color="#fbbf24" />
                      <Text style={{ marginLeft: 4, color: '#6b7280', fontSize: 14 }}>
                        {store.rating} • {store.deliveryTime} • ${store.deliveryFee} delivery
                      </Text>
                    </View>
                    <Text style={{ color: '#9ca3af', fontSize: 14 }}>
                      {store.distance} away • {store.category}
                    </Text>
                  </View>
                  <View style={{
                    backgroundColor: store.isOpen ? '#10b981' : '#ef4444',
                    paddingHorizontal: 8,
                    paddingVertical: 4,
                    borderRadius: 6,
                  }}>
                    <Text style={{ color: '#fff', fontSize: 12, fontWeight: '600' }}>
                      {store.isOpen ? 'Open' : 'Closed'}
                    </Text>
                  </View>
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        <View style={{ height: 100 }} />
      </ScrollView>

      {/* Footer Navigation */}
      <FooterNavigation navigation={navigation} activeScreen="Stores" />
    </SafeAreaView>
  );
}
