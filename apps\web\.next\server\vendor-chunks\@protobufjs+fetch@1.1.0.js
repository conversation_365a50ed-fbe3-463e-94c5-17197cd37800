"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@protobufjs+fetch@1.1.0";
exports.ids = ["vendor-chunks/@protobufjs+fetch@1.1.0"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@protobufjs+fetch@1.1.0/node_modules/@protobufjs/fetch/index.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@protobufjs+fetch@1.1.0/node_modules/@protobufjs/fetch/index.js ***!
  \************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\r\nmodule.exports = fetch;\r\n\r\nvar asPromise = __webpack_require__(/*! @protobufjs/aspromise */ \"(ssr)/../../node_modules/.pnpm/@protobufjs+aspromise@1.1.2/node_modules/@protobufjs/aspromise/index.js\"),\r\n    inquire   = __webpack_require__(/*! @protobufjs/inquire */ \"(ssr)/../../node_modules/.pnpm/@protobufjs+inquire@1.1.0/node_modules/@protobufjs/inquire/index.js\");\r\n\r\nvar fs = inquire(\"fs\");\r\n\r\n/**\r\n * Node-style callback as used by {@link util.fetch}.\r\n * @typedef FetchCallback\r\n * @type {function}\r\n * @param {?Error} error Error, if any, otherwise `null`\r\n * @param {string} [contents] File contents, if there hasn't been an error\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Options as used by {@link util.fetch}.\r\n * @typedef FetchOptions\r\n * @type {Object}\r\n * @property {boolean} [binary=false] Whether expecting a binary response\r\n * @property {boolean} [xhr=false] If `true`, forces the use of XMLHttpRequest\r\n */\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @memberof util\r\n * @param {string} filename File path or url\r\n * @param {FetchOptions} options Fetch options\r\n * @param {FetchCallback} callback Callback function\r\n * @returns {undefined}\r\n */\r\nfunction fetch(filename, options, callback) {\r\n    if (typeof options === \"function\") {\r\n        callback = options;\r\n        options = {};\r\n    } else if (!options)\r\n        options = {};\r\n\r\n    if (!callback)\r\n        return asPromise(fetch, this, filename, options); // eslint-disable-line no-invalid-this\r\n\r\n    // if a node-like filesystem is present, try it first but fall back to XHR if nothing is found.\r\n    if (!options.xhr && fs && fs.readFile)\r\n        return fs.readFile(filename, function fetchReadFileCallback(err, contents) {\r\n            return err && typeof XMLHttpRequest !== \"undefined\"\r\n                ? fetch.xhr(filename, options, callback)\r\n                : err\r\n                ? callback(err)\r\n                : callback(null, options.binary ? contents : contents.toString(\"utf8\"));\r\n        });\r\n\r\n    // use the XHR version otherwise.\r\n    return fetch.xhr(filename, options, callback);\r\n}\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @name util.fetch\r\n * @function\r\n * @param {string} path File path or url\r\n * @param {FetchCallback} callback Callback function\r\n * @returns {undefined}\r\n * @variation 2\r\n */\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @name util.fetch\r\n * @function\r\n * @param {string} path File path or url\r\n * @param {FetchOptions} [options] Fetch options\r\n * @returns {Promise<string|Uint8Array>} Promise\r\n * @variation 3\r\n */\r\n\r\n/**/\r\nfetch.xhr = function fetch_xhr(filename, options, callback) {\r\n    var xhr = new XMLHttpRequest();\r\n    xhr.onreadystatechange /* works everywhere */ = function fetchOnReadyStateChange() {\r\n\r\n        if (xhr.readyState !== 4)\r\n            return undefined;\r\n\r\n        // local cors security errors return status 0 / empty string, too. afaik this cannot be\r\n        // reliably distinguished from an actually empty file for security reasons. feel free\r\n        // to send a pull request if you are aware of a solution.\r\n        if (xhr.status !== 0 && xhr.status !== 200)\r\n            return callback(Error(\"status \" + xhr.status));\r\n\r\n        // if binary data is expected, make sure that some sort of array is returned, even if\r\n        // ArrayBuffers are not supported. the binary string fallback, however, is unsafe.\r\n        if (options.binary) {\r\n            var buffer = xhr.response;\r\n            if (!buffer) {\r\n                buffer = [];\r\n                for (var i = 0; i < xhr.responseText.length; ++i)\r\n                    buffer.push(xhr.responseText.charCodeAt(i) & 255);\r\n            }\r\n            return callback(null, typeof Uint8Array !== \"undefined\" ? new Uint8Array(buffer) : buffer);\r\n        }\r\n        return callback(null, xhr.responseText);\r\n    };\r\n\r\n    if (options.binary) {\r\n        // ref: https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/Sending_and_Receiving_Binary_Data#Receiving_binary_data_in_older_browsers\r\n        if (\"overrideMimeType\" in xhr)\r\n            xhr.overrideMimeType(\"text/plain; charset=x-user-defined\");\r\n        xhr.responseType = \"arraybuffer\";\r\n    }\r\n\r\n    xhr.open(\"GET\", filename);\r\n    xhr.send();\r\n};\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@protobufjs+fetch@1.1.0/node_modules/@protobufjs/fetch/index.js\n");

/***/ })

};
;